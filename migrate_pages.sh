#!/bin/bash

# Migration script to copy and convert pages from old app to new app
echo "🚀 Starting comprehensive migration from saloplus_backoffice to saloplus-dashboard..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Create directories if they don't exist
print_info "Creating directory structure..."
mkdir -p src/views/Loans/Products
mkdir -p src/views/Loans/Accounts
mkdir -p src/views/Loans/Repayments
mkdir -p src/views/System/Roles
mkdir -p src/views/System/Users
mkdir -p src/views/Financial/BillPayments
mkdir -p src/services
mkdir -p src/components/Loan
mkdir -p src/components/System
mkdir -p src/components/Financial

print_status "Created directory structure"

# List of pages to migrate with their priorities
declare -A PAGES_TO_MIGRATE=(
    # High Priority - Core functionality
    ["dashboard"]="saloplus_backoffice/src/views/dashboard/dashboard.vue -> src/views/Dashboard.vue"
    ["loan-products-list"]="saloplus_backoffice/src/views/loan/products/index.vue -> src/views/Loans/Products/ProductsList.vue"
    ["loan-products-add"]="saloplus_backoffice/src/views/loan/products/add.vue -> src/views/Loans/Products/ProductsAdd.vue"
    ["loan-products-edit"]="saloplus_backoffice/src/views/loan/products/edit.vue -> src/views/Loans/Products/ProductsEdit.vue"
    ["loan-accounts-list"]="saloplus_backoffice/src/views/loan/accounts/index.vue -> src/views/Loans/Accounts/AccountsList.vue"
    ["loan-accounts-add"]="saloplus_backoffice/src/views/loan/accounts/add.vue -> src/views/Loans/Accounts/AccountsAdd.vue"
    ["loan-accounts-edit"]="saloplus_backoffice/src/views/loan/accounts/edit.vue -> src/views/Loans/Accounts/AccountsEdit.vue"
    
    # Medium Priority - Important features
    ["loan-repayments"]="saloplus_backoffice/src/views/loan/repayments/index.vue -> src/views/Loans/Repayments/RepaymentsList.vue"
    ["loan-requests"]="saloplus_backoffice/src/views/loan/requests.vue -> src/views/Loans/LoanRequests.vue"
    ["loan-limits"]="saloplus_backoffice/src/views/loan/limits.vue -> src/views/Loans/LoanLimits.vue"
    ["loan-checkoff"]="saloplus_backoffice/src/views/loan/check-off.vue -> src/views/Loans/CheckOff.vue"
    
    # System Management
    ["system-users"]="saloplus_backoffice/src/views/system/users/index.vue -> src/views/System/Users/<USER>"
    ["system-roles"]="saloplus_backoffice/src/views/system/roles/index.vue -> src/views/System/Roles/RolesList.vue"
    
    # Financial
    ["bill-payments"]="saloplus_backoffice/src/views/bill_payments/index.vue -> src/views/Financial/BillPayments/BillPaymentsList.vue"
    ["bill-payments-add"]="saloplus_backoffice/src/views/bill_payments/add.vue -> src/views/Financial/BillPayments/BillPaymentsAdd.vue"
    ["withdrawals"]="saloplus_backoffice/src/views/withdrawals/index.vue -> src/views/Financial/Withdrawals.vue"
    
    # Organization Management (already partially done)
    ["organizations"]="saloplus_backoffice/src/views/organization/index.vue -> src/views/Organizations/OrganizationsList.vue"
    ["organizations-add"]="saloplus_backoffice/src/views/organization/add.vue -> src/views/Organizations/OrganizationsAdd.vue"
    ["organizations-config"]="saloplus_backoffice/src/views/organization/config.vue -> src/views/Organizations/OrganizationsConfig.vue"
    ["organizations-bulk"]="saloplus_backoffice/src/views/organization/send_bulk.vue -> src/views/Organizations/OrganizationsBulkSMS.vue"
)

# List of API services to create
declare -A API_SERVICES=(
    ["loanProductsApi"]="Loan Products API service"
    ["loanAccountsApi"]="Loan Accounts API service"
    ["loanRequestsApi"]="Loan Requests API service"
    ["loanLimitsApi"]="Loan Limits API service"
    ["loanRepaymentsApi"]="Loan Repayments API service"
    ["systemUsersApi"]="System Users API service"
    ["systemRolesApi"]="System Roles API service"
    ["billPaymentsApi"]="Bill Payments API service"
    ["withdrawalsApi"]="Withdrawals API service"
    ["dashboardApi"]="Dashboard Statistics API service"
)

# List of components to create
declare -A COMPONENTS=(
    ["LoanProductForm"]="Reusable loan product form component"
    ["LoanAccountForm"]="Reusable loan account form component"
    ["StatusBadge"]="Status display component"
    ["CurrencyDisplay"]="Currency formatting component"
    ["DateRangePicker"]="Date range picker component"
    ["FilterDropdown"]="Filter dropdown component"
    ["ActionDropdown"]="Action dropdown menu component"
    ["KYCModal"]="KYC verification modal"
    ["BlacklistModal"]="Account blacklist modal"
    ["ConfirmationModal"]="Generic confirmation modal"
)

print_info "Migration plan created with ${#PAGES_TO_MIGRATE[@]} pages, ${#API_SERVICES[@]} API services, and ${#COMPONENTS[@]} components"

# Function to check if old file exists
check_old_file() {
    local old_path="$1"
    if [ ! -f "../$old_path" ]; then
        print_error "Source file not found: $old_path"
        return 1
    fi
    return 0
}

# Function to backup existing file
backup_existing() {
    local new_path="$1"
    if [ -f "$new_path" ]; then
        cp "$new_path" "$new_path.backup.$(date +%Y%m%d_%H%M%S)"
        print_warning "Backed up existing file: $new_path"
    fi
}

# Function to show migration summary
show_migration_summary() {
    print_info "=== MIGRATION SUMMARY ==="
    print_info "Pages to migrate: ${#PAGES_TO_MIGRATE[@]}"
    print_info "API services to create: ${#API_SERVICES[@]}"
    print_info "Components to create: ${#COMPONENTS[@]}"
    echo ""
    print_info "Key features being migrated:"
    print_info "• Complete loan management system"
    print_info "• User and role management"
    print_info "• Financial operations"
    print_info "• Organization management"
    print_info "• Dashboard with statistics"
    echo ""
    print_warning "This migration will:"
    print_warning "• Convert Vue 2 Options API to Vue 3 Composition API"
    print_warning "• Update Vuex store usage to Pinia"
    print_warning "• Modernize styling with Tailwind CSS"
    print_warning "• Implement TypeScript types"
    print_warning "• Add proper error handling"
    echo ""
}

# Function to create API service template
create_api_service() {
    local service_name="$1"
    local description="$2"
    local file_path="src/services/${service_name}.ts"
    
    if [ ! -f "$file_path" ]; then
        print_info "Creating API service: $service_name"
        # This would contain the template for API service
        # For now, just create a placeholder
        echo "// $description - Generated by migration script" > "$file_path"
        echo "// TODO: Implement $service_name" >> "$file_path"
        print_status "Created API service template: $file_path"
    else
        print_warning "API service already exists: $file_path"
    fi
}

# Function to create component template
create_component() {
    local component_name="$1"
    local description="$2"
    local file_path="src/components/${component_name}.vue"
    
    if [ ! -f "$file_path" ]; then
        print_info "Creating component: $component_name"
        # This would contain the template for component
        # For now, just create a placeholder
        cat > "$file_path" << EOF
<template>
  <div>
    <!-- $description -->
    <!-- TODO: Implement $component_name -->
  </div>
</template>

<script setup lang="ts">
// $description - Generated by migration script
// TODO: Implement $component_name
</script>
EOF
        print_status "Created component template: $file_path"
    else
        print_warning "Component already exists: $file_path"
    fi
}

# Main migration function
run_migration() {
    show_migration_summary
    
    echo ""
    read -p "Do you want to proceed with the migration? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Migration cancelled by user"
        exit 0
    fi
    
    print_info "Starting migration process..."
    
    # Create API services
    print_info "Creating API services..."
    for service in "${!API_SERVICES[@]}"; do
        create_api_service "$service" "${API_SERVICES[$service]}"
    done
    
    # Create components
    print_info "Creating components..."
    for component in "${!COMPONENTS[@]}"; do
        create_component "$component" "${COMPONENTS[$component]}"
    done
    
    print_status "Migration templates created successfully!"
    print_info "Next steps:"
    print_info "1. Review the created templates"
    print_info "2. Implement the actual migration logic for each page"
    print_info "3. Update router configuration"
    print_info "4. Test each migrated page"
    print_info "5. Update navigation menus"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
    print_error "Please run this script from the mossbets-b2b-dashboard root directory"
    exit 1
fi

# Run the migration
run_migration
