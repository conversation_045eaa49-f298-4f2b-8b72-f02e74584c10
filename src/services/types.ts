/**
 * Common API types and interfaces
 */

// Base API Response Interface
export interface ApiResponse<T = any> {
  status: number
  message: T
  code?: string
}

// Pagination Interface
export interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
  search?: string
  sortField?: string
  sortDirection?: 'asc' | 'desc'
  // Standard API fields
  start?: string
  end?: string
  status?: string | number
  export?: boolean
  timestamp?: number
}

// Pagination Response
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  total_count?: number // Keep for backward compatibility
  current_page: number
  per_page?: number // Optional since it's not required
  limit?: number // Alternative to per_page
  last_page?: number
  from?: number
  to?: number
}

// User Types
export interface User {
  id: number
  username: string
  display_name: string
  email?: string
  role_id: string
  permissions: Permission[]
  token: string
  mc?: number
  created_at?: string
  updated_at?: string
}

export interface Permission {
  id: number
  name: string
  description?: string
  module?: string
}

export interface Role {
  id: number
  name: string
  description?: string
  permissions: Permission[]
}

// Transaction Types
export interface Transaction {
  id: string
  transaction_id: string
  client_account: string
  customer_phone: string
  transaction_type: string
  amount: number
  balance_before: number
  balance_after: number
  description: string
  status: string
  created_at: string
}

// System User Types
export interface SystemUser {
  id: number
  username: string
  display_name: string
  email: string
  role_id: number
  role_name: string
  client_account?: string
  status: number
  last_login?: string
  created_at: string
}

// Dashboard Stats Types
export interface DashboardStats {
  total_partners: number
  active_partners: number
  total_bets: number
  total_bet_amount: number
  pending_bets: number
  active_bets: number
  settled_bets: number
}

// Error Types
export interface ApiError {
  status: number
  message: string
  code?: string
  details?: any
}

// File Upload Types
export interface FileUploadResponse {
  file_url: string
  file_name: string
  file_size: number
  file_type: string
}

// Bulk SMS Types
export interface BulkSMSRequest {
  client_account: string
  message: string
  recipients: string[]
  sender_id?: string
}

export interface BulkSMSResponse {
  message_id: string
  status: string
  total_recipients: number
  successful_sends: number
  failed_sends: number
}

// Report Types
export interface ReportRequest {
  client_account?: string
  start_date: string
  end_date: string
  report_type: string
  format?: 'pdf' | 'excel' | 'csv'
}

export interface ReportResponse {
  report_url: string
  report_name: string
  generated_at: string
  expires_at: string
}

// Configuration Types
export interface SystemConfig {
  key: string
  value: string
  description?: string
  type: 'string' | 'number' | 'boolean' | 'json'
  client_account?: string
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  created_at: string
}

// Search Types
export interface SearchParams {
  query: string
  type?: 'partner' | 'bet' | 'transaction' | 'service'
  filters?: Record<string, any>
}

export interface SearchResult<T = any> {
  type: string
  data: T
  score: number
}

// Export all types
export type {
  // Re-export for convenience
  ApiResponse as Response,
  PaginationParams as Pagination,
  PaginatedResponse as PaginatedData
}
