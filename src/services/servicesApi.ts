import { apiClient } from './apiClient'
import { createHash<PERSON>ey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

export interface Service {
  id: number
  name: string
  description?: string
  status: number | string
  created_at: string
  updated_at?: string
}

export const servicesApi = {
  /**
   * Get all services
   */
  async getServices(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Service>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search || '',
        status: params.status || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('services/v1/list', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const services = responseData.data?.data || []
      const mappedServices = services.map((service: any) => ({
        id: parseInt(service.service_id || service.id),
        name: service.service_name || service.name,
        description: service.description || '',
        status: service.status,
        created_at: service.created_at || new Date().toISOString(),
        updated_at: service.updated_at || new Date().toISOString()
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedServices,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get services error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create a new service
   */
  async createService(serviceData: {
    name: string
    description?: string
    status?: number | string
  }): Promise<ApiResponse<Service>> {
    try {
      const payload = {
        name: serviceData.name,
        description: serviceData.description || '',
        status: serviceData.status || 1,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('services/v1/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create service error:', error)
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Update a service
   */
  async updateService(serviceId: number, serviceData: {
    name?: string
    description?: string
    status?: number | string
  }): Promise<ApiResponse<Service>> {
    try {
      const payload = {
        name: serviceData.name,
        description: serviceData.description,
        status: serviceData.status,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`services/v1/update/${serviceId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update service error:', error)
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Delete a service
   */
  async deleteService(serviceId: number): Promise<ApiResponse<any>> {
    try {
      const payload = {
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`services/v1/delete/${serviceId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.message || 'Service deleted successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Delete service error:', error)
      
      return {
        status: 500,
        message: 'Failed to delete service',
        code: '500'
      }
    }
  }
}
