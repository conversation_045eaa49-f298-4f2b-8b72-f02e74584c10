import { api<PERSON><PERSON> } from './apiClient'
import { createAuthHash } from '@/utils/hash'
import type { ApiResponse } from './types'

// Types
interface LoginPayload {
  username: string
  password: string
  dial_code: string
}

interface LoginWithCodePayload extends LoginPayload {
  verification_code: string
}

interface ForgotPasswordPayload {
  username: string
  dial_code: string
}

interface User {
  id?: number
  username?: string
  email?: string
  role_id: string
  role_name?: string // Role display name
  permissions?: Permission[]
  token?: string
  mc?: number
  clients?: Client[]
  // New fields from API response
  un?: string // User name (display name)
  cn?: string // Company name
  cid?: string // Company ID
  uid?: string // User ID
  expires?: string // Token expiration
  // OTP related fields
  requires_otp?: boolean
  otp_expires_in?: number
  otp_sent?: boolean
}

interface Permission {
  id: string | number // Can be string from API
  name: string
  description?: string
}

interface Client {
  client_id: string
  client_name: string
  client_account: string
}

interface LoginResponse {
  data: User
  message?: string
}

/**
 * Authentication API service
 */
export const authApi = {
  /**
   * Authenticate user with username and password
   */
  async login(payload: LoginPayload): Promise<ApiResponse<LoginResponse>> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('users/v1/login', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: {
          data: responseData.data,
          message: responseData.message
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Login error:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: null, message: data.statusDescription || 'Server error' } as any,
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: null, message: data.data.message || 'Login failed' } as any,
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: null, message: 'Network error or server unavailable' } as any,
        code: '500'
      }
    }
  },

  /**
   * Authenticate user with verification code
   */
  async loginWithCode(payload: LoginWithCodePayload): Promise<ApiResponse<LoginResponse>> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('users/v1/login_verify', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: {
          data: responseData.data,
          message: responseData.message
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Login with code error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: null, message: data.statusDescription || 'Server error' } as any,
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: null, message: data.data.message || 'Login failed' } as any,
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: null, message: 'Network error or server unavailable' } as any,
        code: '500'
      }
    }
  },

  /**
   * Request password reset for user
   */
  async forgotPassword(payload: ForgotPasswordPayload): Promise<ApiResponse> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('users/v1/password_reset', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Forgot password error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: data.statusDescription || 'Server error',
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: data.data.message || 'Password reset failed',
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Verify reset code
   */
  async verifyResetCode(payload: { username: string; verification_code: string; dial_code: string }): Promise<ApiResponse> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('users/v1/verify_reset_code', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Verify reset code error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Verification failed',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Reset password
   */
  async resetPassword(payload: { 
    username: string
    new_password: string
    otp_code: string
    dial_code: string 
  }): Promise<ApiResponse> {
    try {
      // Encode the new password
      const encodedPayload = {
        ...payload,
        new_password: btoa(payload.new_password)
      }
      
      const hash = createAuthHash(encodedPayload)
      
      const response = await apiClient.post('users/v1/password_change', encodedPayload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Reset password error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Password reset failed',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Logout
   */
  async logout(): Promise<ApiResponse> {
    try {
      const response = await apiClient.post('merchant/v1/logout')

      return {
        status: 200,
        message: 'Logged out successfully',
        code: '200'
      }
    } catch (error: any) {
      console.error('Logout error:', error)

      // Even if logout fails on server, we should clear local data
      return {
        status: 200,
        message: 'Logged out locally',
        code: '200'
      }
    }
  },


}
