<template>
  <div class="relative">
    <ActionButton
      variant="view"
      size="sm"
      shape="round"
      :icon-only="true"
      tooltip="Actions"
      @click="toggleDropdown"
    >
      <template #icon>
        <EllipsisVerticalIcon class="h-4 w-4" />
      </template>
    </ActionButton>

    <!-- Dropdown Menu -->
    <Transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
        @click.stop
      >
        <div class="py-1">
          <!-- Dynamic Actions -->
          <button
            v-for="action in filteredActions"
            :key="action.key"
            @click="handleAction(action)"
            class="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            :class="action.class"
          >
            <component :is="action.icon" class="h-4 w-4 mr-3" />
            {{ action.label }}
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { EllipsisVerticalIcon } from '@heroicons/vue/24/outline'
import ActionButton from '@/components/ActionButton.vue'
import type { TableAction } from './tableActions'

interface Props {
  item: any
  actions?: TableAction[]
  isOpen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  isOpen: false
})

const emit = defineEmits<{
  toggle: []
  action: [actionKey: string, item: any]
}>()



// Computed property to filter actions based on conditions
const filteredActions = computed(() => {
  return props.actions.filter(action => {
    if (action.condition) {
      return action.condition(props.item)
    }
    return true
  })
})

const toggleDropdown = () => {
  emit('toggle')
}

const handleAction = (action: TableAction) => {
  emit('action', action.key, props.item)
}


</script>
