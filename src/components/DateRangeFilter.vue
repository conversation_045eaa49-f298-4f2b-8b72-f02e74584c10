<template>
  <div class="space-y-2">
    <label v-if="label" class="block text-sm font-medium text-gray-700">{{ label }}</label>
    
    <div class="flex items-center space-x-2">
      <!-- Quick Presets -->
      <div v-if="showPresets" class="relative">
        <select
          v-model="selectedPreset"
          @change="applyPreset"
          class="block w-32 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Custom</option>
          <option v-for="preset in presets" :key="preset.value" :value="preset.value">
            {{ preset.label }}
          </option>
        </select>
      </div>

      <!-- Date Inputs -->
      <div class="flex items-center space-x-2">
        <div class="relative">
          <input
            v-model="startDate"
            type="date"
            :max="endDate || maxDate"
            :min="minDate"
            @change="handleDateChange"
            class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Start date"
          />
        </div>
        
        <span class="text-gray-500 text-sm">to</span>
        
        <div class="relative">
          <input
            v-model="endDate"
            type="date"
            :min="startDate || minDate"
            :max="maxDate"
            @change="handleDateChange"
            class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="End date"
          />
        </div>
      </div>

      <!-- Clear Button -->
      <button
        v-if="(startDate || endDate) && clearable"
        @click="clearDates"
        class="inline-flex items-center justify-center w-8 h-8 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <XMarkIcon class="w-4 h-4 text-gray-400" />
      </button>
    </div>

    <!-- Date Range Display -->
    <div v-if="showRangeText && (startDate || endDate)" class="text-xs text-gray-500">
      <span v-if="startDate && endDate">
        {{ formatDateRange(startDate, endDate) }}
      </span>
      <span v-else-if="startDate">
        From {{ formatDate(startDate) }}
      </span>
      <span v-else-if="endDate">
        Until {{ formatDate(endDate) }}
      </span>
    </div>

    <!-- Validation Error -->
    <div v-if="validationError" class="text-xs text-red-600">
      {{ validationError }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

// Props
interface DateRange {
  startDate: string
  endDate: string
}

interface DatePreset {
  value: string
  label: string
  startDate: string
  endDate: string
}

interface Props {
  modelValue?: DateRange
  label?: string
  showPresets?: boolean
  showRangeText?: boolean
  clearable?: boolean
  minDate?: string
  maxDate?: string
  presets?: DatePreset[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ startDate: '', endDate: '' }),
  label: '',
  showPresets: true,
  showRangeText: true,
  clearable: true,
  minDate: '',
  maxDate: '',
  presets: () => [
    {
      value: 'today',
      label: 'Today',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    {
      value: 'yesterday',
      label: 'Yesterday',
      startDate: new Date(Date.now() - 86400000).toISOString().split('T')[0],
      endDate: new Date(Date.now() - 86400000).toISOString().split('T')[0]
    },
    {
      value: 'last7days',
      label: 'Last 7 days',
      startDate: new Date(Date.now() - 7 * 86400000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    {
      value: 'last30days',
      label: 'Last 30 days',
      startDate: new Date(Date.now() - 30 * 86400000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    {
      value: 'thismonth',
      label: 'This month',
      startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    {
      value: 'lastmonth',
      label: 'Last month',
      startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toISOString().split('T')[0],
      endDate: new Date(new Date().getFullYear(), new Date().getMonth(), 0).toISOString().split('T')[0]
    }
  ]
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: DateRange]
  'change': [value: DateRange]
}>()

// Local state
const startDate = ref(props.modelValue.startDate)
const endDate = ref(props.modelValue.endDate)
const selectedPreset = ref('')

// Computed properties
const validationError = computed(() => {
  if (startDate.value && endDate.value && startDate.value > endDate.value) {
    return 'Start date cannot be after end date'
  }
  return ''
})

// Methods
const handleDateChange = () => {
  selectedPreset.value = '' // Clear preset when manually changing dates
  
  if (!validationError.value) {
    const dateRange = {
      startDate: startDate.value,
      endDate: endDate.value
    }
    emit('update:modelValue', dateRange)
    emit('change', dateRange)
  }
}

const applyPreset = () => {
  if (!selectedPreset.value) return
  
  const preset = props.presets.find(p => p.value === selectedPreset.value)
  if (preset) {
    startDate.value = preset.startDate
    endDate.value = preset.endDate
    handleDateChange()
  }
}

const clearDates = () => {
  startDate.value = ''
  endDate.value = ''
  selectedPreset.value = ''
  
  const dateRange = {
    startDate: '',
    endDate: ''
  }
  emit('update:modelValue', dateRange)
  emit('change', dateRange)
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateRange = (start: string, end: string) => {
  if (!start || !end) return ''
  
  const startFormatted = formatDate(start)
  const endFormatted = formatDate(end)
  
  if (start === end) {
    return startFormatted
  }
  
  const daysDiff = Math.ceil((new Date(end).getTime() - new Date(start).getTime()) / (1000 * 60 * 60 * 24))
  
  return `${startFormatted} - ${endFormatted} (${daysDiff + 1} days)`
}

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  startDate.value = newValue.startDate
  endDate.value = newValue.endDate
  
  // Check if current dates match any preset
  const matchingPreset = props.presets.find(preset => 
    preset.startDate === newValue.startDate && preset.endDate === newValue.endDate
  )
  selectedPreset.value = matchingPreset?.value || ''
}, { deep: true, immediate: true })
</script>
