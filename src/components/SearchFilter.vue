<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
      <!-- Search Input -->
      <div class="flex-1 max-w-md">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            v-model="localFilters.search"
            type="text"
            :placeholder="searchPlaceholder"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
            @input="handleSearchInput"
          />
        </div>
      </div>

      <!-- Filter Toggles -->
      <div class="flex items-center space-x-2">
        <!-- Status Filter -->
        <div v-if="showStatusFilter" class="relative">
          <select
            v-model="localFilters.status"
            @change="handleFilterChange"
            class="block w-32 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option v-for="status in statusOptions" :key="status.value" :value="status.value">
              {{ status.label }}
            </option>
          </select>
        </div>

        <!-- Role Filter (for users) -->
        <div v-if="showRoleFilter" class="relative">
          <select
            v-model="localFilters.role_id"
            @change="handleFilterChange"
            class="block w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Roles</option>
            <option v-for="role in roleOptions" :key="role.role_id" :value="role.role_id">
              {{ role.role_name }}
            </option>
          </select>
        </div>

        <!-- Module Filter (for permissions) -->
        <div v-if="showModuleFilter" class="relative">
          <select
            v-model="localFilters.module"
            @change="handleFilterChange"
            class="block w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Modules</option>
            <option v-for="module in moduleOptions" :key="module.value" :value="module.value">
              {{ module.label }}
            </option>
          </select>
        </div>

        <!-- Advanced Filters Toggle -->
        <button
          v-if="hasAdvancedFilters"
          @click="toggleAdvancedFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <AdjustmentsHorizontalIcon class="h-4 w-4 mr-2" />
          Filters
          <ChevronDownIcon 
            :class="['h-4 w-4 ml-1 transition-transform duration-200', { 'rotate-180': showAdvancedFilters }]" 
          />
        </button>

        <!-- Clear Filters -->
        <button
          v-if="hasActiveFilters"
          @click="clearFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <XMarkIcon class="h-4 w-4 mr-2" />
          Clear
        </button>
      </div>
    </div>

    <!-- Advanced Filters Panel -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-96"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 max-h-96"
      leave-to-class="opacity-0 max-h-0"
    >
      <div v-if="showAdvancedFilters && hasAdvancedFilters" class="mt-4 pt-4 border-t border-gray-200 overflow-hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- Date Range Filter -->
          <div v-if="showDateFilter">
            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <div class="grid grid-cols-2 gap-2">
              <input
                v-model="localFilters.start_date"
                type="date"
                @change="handleFilterChange"
                class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <input
                v-model="localFilters.end_date"
                type="date"
                @change="handleFilterChange"
                class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <!-- Client Filter (for super users) -->
          <div v-if="showClientFilter">
            <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
            <select
              v-model="localFilters.client_id"
              @change="handleFilterChange"
              class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Clients</option>
              <option v-for="client in clientOptions" :key="client.value" :value="client.value">
                {{ client.label }}
              </option>
            </select>
          </div>

          <!-- Custom Slot for Additional Filters -->
          <slot name="advanced-filters" :filters="localFilters" :handleChange="handleFilterChange"></slot>
        </div>

        <!-- Apply/Reset Buttons -->
        <div class="flex items-center justify-end space-x-3 mt-4">
          <button
            @click="resetAdvancedFilters"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Reset
          </button>
          <button
            @click="applyFilters"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

// Props
interface Props {
  filters?: Record<string, any>
  searchPlaceholder?: string
  showStatusFilter?: boolean
  showRoleFilter?: boolean
  showModuleFilter?: boolean
  showDateFilter?: boolean
  showClientFilter?: boolean
  hasAdvancedFilters?: boolean
  statusOptions?: Array<{ value: string; label: string }>
  roleOptions?: Array<{ role_id: number; role_name: string }>
  moduleOptions?: Array<{ value: string; label: string }>
  clientOptions?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<Props>(), {
  filters: () => ({}),
  searchPlaceholder: 'Search...',
  showStatusFilter: true,
  showRoleFilter: false,
  showModuleFilter: false,
  showDateFilter: false,
  showClientFilter: false,
  hasAdvancedFilters: false,
  statusOptions: () => [
    { value: '1', label: 'Active' },
    { value: '0', label: 'Inactive' }
  ],
  roleOptions: () => [],
  moduleOptions: () => [],
  clientOptions: () => []
})

// Emits
const emit = defineEmits<{
  'update:filters': [filters: Record<string, any>]
  'search': [query: string]
  'filter': [filters: Record<string, any>]
  'clear': []
}>()

// Local state
const localFilters = reactive({ ...props.filters })
const showAdvancedFilters = ref(false)
let searchTimeout: number

// Computed properties
const hasActiveFilters = computed(() => {
  return Object.values(localFilters).some(value => value !== '' && value !== null && value !== undefined)
})

// Methods
const handleSearchInput = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    emit('search', localFilters.search || '')
    emit('update:filters', { ...localFilters })
  }, 300) as unknown as number
}

const handleFilterChange = () => {
  emit('filter', { ...localFilters })
  emit('update:filters', { ...localFilters })
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

const clearFilters = () => {
  Object.keys(localFilters).forEach(key => {
    localFilters[key] = ''
  })
  emit('clear')
  emit('update:filters', { ...localFilters })
}

const resetAdvancedFilters = () => {
  // Only reset advanced filter fields, keep basic search and status
  const basicFields = ['search', 'status', 'role_id', 'module']
  Object.keys(localFilters).forEach(key => {
    if (!basicFields.includes(key)) {
      localFilters[key] = ''
    }
  })
  handleFilterChange()
}

const applyFilters = () => {
  emit('filter', { ...localFilters })
  showAdvancedFilters.value = false
}

// Watch for prop changes
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters, newFilters)
}, { deep: true })
</script>
