<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
    @click="closeModal"
  >
    <div
      class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
      @click.stop
    >
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          {{ modalTitle }}
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="mt-4">
        <!-- Search -->
        <div class="mb-4">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Load All Button -->
        <div class="mb-4 flex justify-between items-center">
          <button
            @click="loadAllItems"
            :disabled="loading"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Loading...' : 'Load All' }}
          </button>
          
          <div class="text-sm text-gray-500">
            {{ selectedItems.length }} selected
          </div>
        </div>

        <!-- Items List -->
        <div class="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
          <div v-if="loading && items.length === 0" class="p-8 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-2 text-sm text-gray-500">Loading...</p>
          </div>
          
          <div v-else-if="filteredItems.length === 0" class="p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <p class="mt-2 text-sm text-gray-500">No items found</p>
          </div>
          
          <div v-else class="divide-y divide-gray-200">
            <label
              v-for="item in filteredItems"
              :key="item.id"
              class="flex items-center p-4 hover:bg-gray-50 cursor-pointer"
            >
              <input
                type="checkbox"
                :value="item.id"
                v-model="selectedItems"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div class="ml-3 flex-1">
                <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                <div v-if="item.description" class="text-sm text-gray-500">{{ item.description }}</div>
                <div v-if="item.module" class="mt-1">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ item.module }}
                  </span>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-6 flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          @click="closeModal"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          @click="assignItems"
          :disabled="selectedItems.length === 0 || assigning"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="assigning" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          {{ assigning ? 'Assigning...' : `Assign ${selectedItems.length} ${assignmentType}${selectedItems.length !== 1 ? 's' : ''}` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { systemApi } from '@/services/systemApi'

// Props
interface Props {
  isOpen: boolean
  type: 'permission-to-user' | 'permission-to-role' | 'user-to-role' | 'role-to-user'
  targetId?: number | string
  targetName?: string
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  type: 'permission-to-user'
})

// Emits
const emit = defineEmits<{
  close: []
  assigned: [items: any[]]
}>()

// Reactive data
const loading = ref(false)
const assigning = ref(false)
const searchQuery = ref('')
const selectedItems = ref<number[]>([])
const items = ref<any[]>([])

// Computed properties
const modalTitle = computed(() => {
  switch (props.type) {
    case 'permission-to-user':
      return `Add Permissions to User: ${props.targetName || 'Unknown'}`
    case 'permission-to-role':
      return `Add Permissions to Role: ${props.targetName || 'Unknown'}`
    case 'user-to-role':
      return `Add Users to Role: ${props.targetName || 'Unknown'}`
    case 'role-to-user':
      return `Add Roles to User: ${props.targetName || 'Unknown'}`
    default:
      return 'Assign Items'
  }
})

const searchPlaceholder = computed(() => {
  switch (props.type) {
    case 'permission-to-user':
    case 'permission-to-role':
      return 'Search permissions...'
    case 'user-to-role':
    case 'role-to-user':
      return 'Search users...'
    default:
      return 'Search...'
  }
})

const assignmentType = computed(() => {
  switch (props.type) {
    case 'permission-to-user':
    case 'permission-to-role':
      return 'permission'
    case 'user-to-role':
    case 'role-to-user':
      return 'user'
    default:
      return 'item'
  }
})

const filteredItems = computed(() => {
  if (!searchQuery.value) return items.value
  
  const query = searchQuery.value.toLowerCase()
  return items.value.filter(item =>
    item.name.toLowerCase().includes(query) ||
    (item.description && item.description.toLowerCase().includes(query)) ||
    (item.module && item.module.toLowerCase().includes(query))
  )
})

// Methods
const loadAllItems = async () => {
  loading.value = true
  try {
    let response
    
    switch (props.type) {
      case 'permission-to-user':
      case 'permission-to-role':
        response = await systemApi.getPermissions({ limit: 1000 })
        break
      case 'user-to-role':
      case 'role-to-user':
        response = await systemApi.getSystemUsers({ limit: 1000 })
        break
      default:
        return
    }
    
    if (response.status === 200) {
      items.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load items:', error)
  } finally {
    loading.value = false
  }
}

const assignItems = async () => {
  if (selectedItems.value.length === 0) return
  
  assigning.value = true
  try {
    // Here you would call the appropriate API to assign the items
    // For now, we'll just emit the selected items
    const selectedItemsData = items.value.filter(item => selectedItems.value.includes(item.id))
    emit('assigned', selectedItemsData)
    closeModal()
  } catch (error) {
    console.error('Failed to assign items:', error)
  } finally {
    assigning.value = false
  }
}

const closeModal = () => {
  emit('close')
  // Reset state
  searchQuery.value = ''
  selectedItems.value = []
  items.value = []
}

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    // Auto-load items when modal opens
    loadAllItems()
  } else {
    // Reset state when modal closes
    searchQuery.value = ''
    selectedItems.value = []
    items.value = []
  }
})
</script>
