<template>
  <button
    :type="type"
    :disabled="disabled"
    :class="buttonClasses"
    @click="handleClick"
    :title="tooltip"
  >
    <!-- Icon Slot -->
    <slot name="icon">
      <!-- Default Icons based on variant -->
      <component 
        :is="defaultIcon" 
        :class="iconClasses"
        v-if="defaultIcon"
      />
    </slot>
    
    <!-- Text (optional) -->
    <span v-if="$slots.default || text" :class="textClasses">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  PencilIcon, 
  TrashIcon, 
  EyeIcon, 
  CheckIcon, 
  XMarkIcon,
  PlusIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  BanknotesIcon
} from '@heroicons/vue/24/outline'

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'edit' | 'delete' | 'view' | 'approve' | 'reject' | 'add' | 'download' | 'settings' | 'users' | 'transactions'
  size?: 'xs' | 'sm' | 'md' | 'lg'
  shape?: 'round' | 'square' | 'rounded'
  disabled?: boolean
  loading?: boolean
  type?: 'button' | 'submit' | 'reset'
  text?: string
  tooltip?: string
  iconOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  shape: 'round',
  disabled: false,
  loading: false,
  type: 'button',
  iconOnly: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// Default icons for different variants
const defaultIconMap = {
  edit: PencilIcon,
  delete: TrashIcon,
  view: EyeIcon,
  approve: CheckIcon,
  reject: XMarkIcon,
  add: PlusIcon,
  download: ArrowDownTrayIcon,
  settings: Cog6ToothIcon,
  users: UserGroupIcon,
  transactions: BanknotesIcon,
  primary: null,
  secondary: null,
  success: CheckIcon,
  danger: XMarkIcon,
  warning: null,
  info: null
}

const defaultIcon = computed(() => defaultIconMap[props.variant])

// Size classes
const sizeClasses = {
  xs: 'h-6 w-6 text-xs',
  sm: 'h-8 w-8 text-sm',
  md: 'h-10 w-10 text-base',
  lg: 'h-12 w-12 text-lg'
}

// Shape classes
const shapeClasses = {
  round: 'rounded-full',
  square: 'rounded-none',
  rounded: 'rounded-lg'
}

// Variant color classes
const variantClasses = {
  primary: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
  secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
  success: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700',
  danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
  warning: 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600 hover:border-yellow-700',
  info: 'bg-cyan-600 hover:bg-cyan-700 text-white border-cyan-600 hover:border-cyan-700',
  edit: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
  delete: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
  view: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
  approve: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700',
  reject: 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700',
  add: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700',
  download: 'bg-indigo-600 hover:bg-indigo-700 text-white border-indigo-600 hover:border-indigo-700',
  settings: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600 hover:border-gray-700',
  users: 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600 hover:border-purple-700',
  transactions: 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700'
}

const buttonClasses = computed(() => [
  'inline-flex items-center justify-center border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
  sizeClasses[props.size],
  shapeClasses[props.shape],
  variantClasses[props.variant],
  {
    'opacity-50 cursor-not-allowed': props.disabled || props.loading,
    'hover:shadow-lg transform hover:scale-105': !props.disabled && !props.loading,
    'gap-2': !props.iconOnly && (props.text || defaultIcon.value)
  }
])

const iconClasses = computed(() => {
  const baseSize = props.size === 'xs' ? 'h-3 w-3' : 
                   props.size === 'sm' ? 'h-4 w-4' : 
                   props.size === 'md' ? 'h-5 w-5' : 'h-6 w-6'
  
  return [baseSize, 'flex-shrink-0']
})

const textClasses = computed(() => [
  'font-medium',
  {
    'sr-only': props.iconOnly
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
