<template>
  <div class="inline-flex items-center space-x-2">
    <!-- Status Badge Display -->
    <div v-if="displayMode === 'badge'" class="flex items-center space-x-1">
      <span
        v-for="status in selectedStatuses"
        :key="status.value"
        :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          getStatusClass(status.value)
        ]"
      >
        {{ status.label }}
        <button
          v-if="multiple"
          @click="removeStatus(status.value)"
          class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black hover:bg-opacity-20"
        >
          <XMarkIcon class="w-3 h-3" />
        </button>
      </span>
      <button
        v-if="!readonly"
        @click="toggleDropdown"
        class="inline-flex items-center justify-center w-6 h-6 rounded-full border border-gray-300 hover:bg-gray-50"
      >
        <PlusIcon class="w-4 h-4 text-gray-400" />
      </button>
    </div>

    <!-- Dropdown Select -->
    <div v-else class="relative">
      <select
        v-if="!multiple"
        v-model="selectedValue"
        @change="handleSingleChange"
        :disabled="readonly"
        :class="[
          'block px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
          readonly ? 'bg-gray-100 cursor-not-allowed' : 'bg-white',
          size === 'sm' ? 'w-24' : size === 'md' ? 'w-32' : 'w-40'
        ]"
      >
        <option value="">{{ placeholder }}</option>
        <option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.label }}
        </option>
      </select>

      <!-- Multi-select Dropdown -->
      <div v-else>
        <button
          @click="toggleDropdown"
          :disabled="readonly"
          :class="[
            'relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm',
            readonly ? 'bg-gray-100 cursor-not-allowed' : ''
          ]"
        >
          <span v-if="selectedStatuses.length === 0" class="text-gray-500">{{ placeholder }}</span>
          <span v-else class="block truncate">
            {{ selectedStatuses.map(s => s.label).join(', ') }}
          </span>
          <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDownIcon class="h-5 w-5 text-gray-400" />
          </span>
        </button>

        <!-- Dropdown Menu -->
        <Transition
          enter-active-class="transition ease-out duration-100"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-75"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <div
            v-if="showDropdown && !readonly"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
          >
            <div
              v-for="option in options"
              :key="option.value"
              @click="toggleStatus(option)"
              class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"
            >
              <div class="flex items-center">
                <span :class="['block truncate', isSelected(option.value) ? 'font-medium' : 'font-normal']">
                  {{ option.label }}
                </span>
              </div>
              <span v-if="isSelected(option.value)" class="absolute inset-y-0 right-0 flex items-center pr-4">
                <CheckIcon class="h-5 w-5 text-blue-600" />
              </span>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import {
  ChevronDownIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'

// Props
interface StatusOption {
  value: string | number
  label: string
  color?: string
}

interface Props {
  modelValue?: string | number | (string | number)[]
  options?: StatusOption[]
  placeholder?: string
  multiple?: boolean
  readonly?: boolean
  displayMode?: 'dropdown' | 'badge'
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  options: () => [
    { value: '1', label: 'Active', color: 'green' },
    { value: '0', label: 'Inactive', color: 'red' },
    { value: '2', label: 'Pending', color: 'yellow' },
    { value: '3', label: 'Suspended', color: 'orange' }
  ],
  placeholder: 'Select status',
  multiple: false,
  readonly: false,
  displayMode: 'dropdown',
  size: 'md'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | (string | number)[]]
  'change': [value: string | number | (string | number)[]]
}>()

// Local state
const showDropdown = ref(false)
const selectedValue = ref(props.multiple ? [] : props.modelValue)

// Computed properties
const selectedStatuses = computed(() => {
  if (!props.multiple) {
    const option = props.options.find(opt => opt.value === selectedValue.value)
    return option ? [option] : []
  }
  
  const values = Array.isArray(selectedValue.value) ? selectedValue.value : []
  return props.options.filter(opt => values.includes(opt.value))
})

// Methods
const toggleDropdown = () => {
  if (!props.readonly) {
    showDropdown.value = !showDropdown.value
  }
}

const closeDropdown = () => {
  showDropdown.value = false
}

const handleSingleChange = () => {
  emit('update:modelValue', selectedValue.value)
  emit('change', selectedValue.value)
}

const toggleStatus = (option: StatusOption) => {
  if (props.multiple) {
    const values = Array.isArray(selectedValue.value) ? [...selectedValue.value] : []
    const index = values.indexOf(option.value)
    
    if (index > -1) {
      values.splice(index, 1)
    } else {
      values.push(option.value)
    }
    
    selectedValue.value = values
    emit('update:modelValue', values)
    emit('change', values)
  } else {
    selectedValue.value = option.value
    emit('update:modelValue', option.value)
    emit('change', option.value)
    closeDropdown()
  }
}

const removeStatus = (value: string | number) => {
  if (props.multiple && Array.isArray(selectedValue.value)) {
    const values = [...selectedValue.value]
    const index = values.indexOf(value)
    if (index > -1) {
      values.splice(index, 1)
      selectedValue.value = values
      emit('update:modelValue', values)
      emit('change', values)
    }
  }
}

const isSelected = (value: string | number) => {
  if (props.multiple) {
    return Array.isArray(selectedValue.value) && selectedValue.value.includes(value)
  }
  return selectedValue.value === value
}

const getStatusClass = (value: string | number) => {
  const option = props.options.find(opt => opt.value === value)
  const color = option?.color || 'gray'
  
  const colorClasses = {
    green: 'bg-green-100 text-green-800',
    red: 'bg-red-100 text-red-800',
    yellow: 'bg-yellow-100 text-yellow-800',
    blue: 'bg-blue-100 text-blue-800',
    orange: 'bg-orange-100 text-orange-800',
    gray: 'bg-gray-100 text-gray-800'
  }
  
  return colorClasses[color as keyof typeof colorClasses] || colorClasses.gray
}

// Handle clicks outside to close dropdown
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    closeDropdown()
  }
}

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
}, { immediate: true })

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
