<template>
  <span
    :class="badgeClasses"
    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
  >
    <div v-if="showDot" :class="dotClasses" class="w-1.5 h-1.5 rounded-full mr-1.5"></div>
    {{ displayText }}
  </span>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'

// Props
const props = defineProps<{
  status: string | number
  type?: 'default' | 'loan' | 'payment' | 'user' | 'kyc' | 'transaction' | 'organization'
  showDot?: boolean
  customText?: string
}>()

// Status mappings for different types
const statusMappings = {
  default: {
    1: { text: 'Active', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    0: { text: 'Inactive', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    3: { text: 'Suspended', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' }
  },
  loan: {
    1: { text: 'Pending', class: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    2: { text: 'Approved', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    3: { text: 'Rejected', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    4: { text: 'Disbursed', class: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' },
    5: { text: 'Repaid', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    6: { text: 'Defaulted', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    7: { text: 'Cancelled', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' }
  },
  payment: {
    1: { text: 'Pending', class: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    2: { text: 'Completed', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    3: { text: 'Failed', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    4: { text: 'Cancelled', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    5: { text: 'Processing', class: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' }
  },
  user: {
    1: { text: 'Active', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    0: { text: 'Inactive', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    2: { text: 'Suspended', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    3: { text: 'Blocked', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' }
  },
  kyc: {
    1: { text: 'Pending', class: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    2: { text: 'Approved', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    3: { text: 'Rejected', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    4: { text: 'Under Review', class: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' }
  },
  transaction: {
    1: { text: 'Success', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    2: { text: 'Failed', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    3: { text: 'Pending', class: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    4: { text: 'Cancelled', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' }
  },
  organization: {
    1: { text: 'Active', class: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    0: { text: 'Inactive', class: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    2: { text: 'Suspended', class: 'bg-red-100 text-red-800', dot: 'bg-red-400' }
  }
}

// Computed properties
const statusConfig = computed(() => {
  const type = props.type || 'default'
  const statusKey = String(props.status)
  const mapping = statusMappings[type]
  
  if (mapping && mapping[statusKey]) {
    return mapping[statusKey]
  }
  
  // Fallback for unknown status
  return {
    text: props.customText || `Status ${props.status}`,
    class: 'bg-gray-100 text-gray-800',
    dot: 'bg-gray-400'
  }
})

const displayText = computed(() => {
  return props.customText || statusConfig.value.text
})

const badgeClasses = computed(() => {
  return statusConfig.value.class
})

const dotClasses = computed(() => {
  return statusConfig.value.dot
})
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
