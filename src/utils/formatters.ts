/**
 * Utility functions for formatting currency, dates, and other data types
 * Used in templates and components for consistent data display
 */

/**
 * Format currency with proper decimal places and currency symbol
 * @param amount - The amount to format
 * @param currency - Currency code (default: 'KES')
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number | string | null | undefined,
  currency: string = 'KES',
  decimals: number = 2
): string {
  if (amount === null || amount === undefined || amount === '') {
    return `${currency} 0.00`
  }

  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) {
    return `${currency} 0.00`
  }

  // Format with proper decimal places and thousands separator
  const formatted = numAmount.toLocaleString('en-KE', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })

  return `${currency} ${formatted}`
}

/**
 * Format date with various format options
 * @param date - Date string, Date object, or timestamp
 * @param format - Format type
 * @param timezone - Timezone (default: 'Africa/Nairobi')
 * @returns Formatted date string
 */
export function formatDate(
  date: string | Date | number | null | undefined,
  format: 'short' | 'long' | 'datetime' | 'time' | 'iso' | 'relative' = 'short',
  timezone: string = 'Africa/Nairobi'
): string {
  if (!date) {
    return 'N/A'
  }

  let dateObj: Date

  try {
    if (typeof date === 'string') {
      dateObj = new Date(date)
    } else if (typeof date === 'number') {
      // Handle both seconds and milliseconds timestamps
      dateObj = new Date(date < 10000000000 ? date * 1000 : date)
    } else {
      dateObj = date
    }

    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date'
    }
  } catch (error) {
    return 'Invalid Date'
  }

  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone
  }

  switch (format) {
    case 'short':
      // dd/mm/yyyy
      options.day = '2-digit'
      options.month = '2-digit'
      options.year = 'numeric'
      return dateObj.toLocaleDateString('en-GB', options)

    case 'long':
      // 15 January 2024
      options.day = 'numeric'
      options.month = 'long'
      options.year = 'numeric'
      return dateObj.toLocaleDateString('en-GB', options)

    case 'datetime':
      // dd/mm/yyyy hh:mm
      options.day = '2-digit'
      options.month = '2-digit'
      options.year = 'numeric'
      options.hour = '2-digit'
      options.minute = '2-digit'
      options.hour12 = false
      return dateObj.toLocaleString('en-GB', options)

    case 'time':
      // hh:mm
      options.hour = '2-digit'
      options.minute = '2-digit'
      options.hour12 = false
      return dateObj.toLocaleTimeString('en-GB', options)

    case 'iso':
      // ISO string
      return dateObj.toISOString()

    case 'relative':
      // Relative time (e.g., "2 hours ago")
      return formatRelativeTime(dateObj)

    default:
      return dateObj.toLocaleDateString('en-GB', options)
  }
}

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 * @param date - Date to compare with now
 * @returns Relative time string
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  const diffWeeks = Math.floor(diffDays / 7)
  const diffMonths = Math.floor(diffDays / 30)
  const diffYears = Math.floor(diffDays / 365)

  if (Math.abs(diffSeconds) < 60) {
    return 'Just now'
  } else if (Math.abs(diffMinutes) < 60) {
    return diffMinutes > 0 ? `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffMinutes)} minute${Math.abs(diffMinutes) !== 1 ? 's' : ''}`
  } else if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffHours)} hour${Math.abs(diffHours) !== 1 ? 's' : ''}`
  } else if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `${diffDays} day${diffDays !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`
  } else if (Math.abs(diffWeeks) < 4) {
    return diffWeeks > 0 ? `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffWeeks)} week${Math.abs(diffWeeks) !== 1 ? 's' : ''}`
  } else if (Math.abs(diffMonths) < 12) {
    return diffMonths > 0 ? `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffMonths)} month${Math.abs(diffMonths) !== 1 ? 's' : ''}`
  } else {
    return diffYears > 0 ? `${diffYears} year${diffYears !== 1 ? 's' : ''} ago` : `in ${Math.abs(diffYears)} year${Math.abs(diffYears) !== 1 ? 's' : ''}`
  }
}

/**
 * Format percentage with proper decimal places
 * @param value - The value to format as percentage
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(
  value: number | string | null | undefined,
  decimals: number = 2
): string {
  if (value === null || value === undefined || value === '') {
    return '0.00%'
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value
  
  if (isNaN(numValue)) {
    return '0.00%'
  }

  return `${numValue.toFixed(decimals)}%`
}

/**
 * Format file size in human readable format
 * @param bytes - Size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number | null | undefined): string {
  if (!bytes || bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * Format phone number for display
 * @param phone - Phone number string
 * @param countryCode - Country code (default: '+254')
 * @returns Formatted phone number
 */
export function formatPhoneNumber(
  phone: string | null | undefined,
  countryCode: string = '+254'
): string {
  if (!phone) return 'N/A'

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Handle Kenyan numbers
  if (countryCode === '+254') {
    if (cleaned.startsWith('254')) {
      return `+${cleaned}`
    } else if (cleaned.startsWith('0')) {
      return `+254${cleaned.substring(1)}`
    } else if (cleaned.length === 9) {
      return `+254${cleaned}`
    }
  }

  return phone
}

/**
 * Truncate text with ellipsis
 * @param text - Text to truncate
 * @param length - Maximum length
 * @returns Truncated text
 */
export function truncateText(
  text: string | null | undefined,
  length: number = 50
): string {
  if (!text) return ''
  
  if (text.length <= length) return text
  
  return `${text.substring(0, length)}...`
}

/**
 * Format status with proper capitalization
 * @param status - Status value
 * @returns Formatted status string
 */
export function formatStatus(status: string | number | null | undefined): string {
  if (status === null || status === undefined) return 'Unknown'
  
  if (typeof status === 'number') {
    return status === 1 ? 'Active' : 'Inactive'
  }
  
  return status.toString().charAt(0).toUpperCase() + status.toString().slice(1).toLowerCase()
}

/**
 * Auto-detect and format data based on type
 * @param value - Value to format
 * @param key - Data key for context
 * @returns Formatted value
 */
export function autoFormat(value: any, key: string): string {
  if (value === null || value === undefined) return 'N/A'

  const keyLower = key.toLowerCase()

  // Currency fields
  if (keyLower.includes('amount') || keyLower.includes('balance') || keyLower.includes('price') || keyLower.includes('cost')) {
    return formatCurrency(value)
  }

  // Date fields
  if (keyLower.includes('date') || keyLower.includes('time') || keyLower.includes('created') || keyLower.includes('updated')) {
    return formatDate(value, 'datetime')
  }

  // Phone fields
  if (keyLower.includes('phone') || keyLower.includes('mobile') || keyLower.includes('msisdn')) {
    return formatPhoneNumber(value)
  }

  // Status fields
  if (keyLower.includes('status')) {
    return formatStatus(value)
  }

  // Percentage fields
  if (keyLower.includes('percent') || keyLower.includes('rate')) {
    return formatPercentage(value)
  }

  // Default: return as string
  return value.toString()
}

// Export all formatters
export default {
  formatCurrency,
  formatDate,
  formatRelativeTime,
  formatPercentage,
  formatFileSize,
  formatPhoneNumber,
  truncateText,
  formatStatus,
  autoFormat
}
