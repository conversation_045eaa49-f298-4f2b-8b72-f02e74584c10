import { useAuthStore } from '@/stores/auth'

/**
 * Permission and Role Utility Functions
 * 
 * These utilities help check user permissions and roles for UI functionality
 * such as showing/hiding menus, buttons, or blocking certain actions.
 */

export interface Permission {
  id: string | number
  name: string
  description?: string
}

export interface Role {
  id: string | number
  name: string
  description?: string
}

/**
 * Check if the current user has a specific permission
 * @param permissionId - The permission ID to check
 * @returns boolean - true if user has the permission
 */
export function hasPermission(permissionId: string | number): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.permissions) {
    return false
  }
  
  return user.permissions.some(permission => 
    permission.id.toString() === permissionId.toString()
  )
}

/**
 * Check if the current user has a specific permission by name
 * @param permissionName - The permission name to check
 * @returns boolean - true if user has the permission
 */
export function hasPermissionByName(permissionName: string): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.permissions) {
    return false
  }
  
  return user.permissions.some(permission => 
    permission.name.toLowerCase() === permissionName.toLowerCase()
  )
}

/**
 * Check if the current user has any of the specified permissions
 * @param permissionIds - Array of permission IDs to check
 * @returns boolean - true if user has at least one of the permissions
 */
export function hasAnyPermission(permissionIds: (string | number)[]): boolean {
  return permissionIds.some(permissionId => hasPermission(permissionId))
}

/**
 * Check if the current user has all of the specified permissions
 * @param permissionIds - Array of permission IDs to check
 * @returns boolean - true if user has all the permissions
 */
export function hasAllPermissions(permissionIds: (string | number)[]): boolean {
  return permissionIds.every(permissionId => hasPermission(permissionId))
}

/**
 * Check if the current user has a specific role
 * @param roleId - The role ID to check
 * @returns boolean - true if user has the role
 */
export function hasRole(roleId: string | number): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.role_id) {
    return false
  }
  
  return user.role_id.toString() === roleId.toString()
}

/**
 * Check if the current user has a specific role by name
 * @param roleName - The role name to check
 * @returns boolean - true if user has the role
 */
export function hasRoleByName(roleName: string): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.role_name) {
    return false
  }
  
  return user.role_name.toLowerCase() === roleName.toLowerCase()
}

/**
 * Check if the current user has any of the specified roles
 * @param roleIds - Array of role IDs to check
 * @returns boolean - true if user has at least one of the roles
 */
export function hasAnyRole(roleIds: (string | number)[]): boolean {
  return roleIds.some(roleId => hasRole(roleId))
}

/**
 * Check if the current user is a super user/admin
 * @returns boolean - true if user is super user
 */
export function isSuperUser(): boolean {
  const authStore = useAuthStore()
  return authStore.isSuperUser
}

/**
 * Check if the current user can access a specific module
 * @param moduleName - The module name to check
 * @returns boolean - true if user can access the module
 */
export function canAccessModule(moduleName: string): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.permissions) {
    return false
  }
  
  // Check if user has any permission for the module
  return user.permissions.some(permission => 
    permission.name.toLowerCase().includes(moduleName.toLowerCase())
  )
}

/**
 * Get all user permissions
 * @returns Permission[] - Array of user permissions
 */
export function getUserPermissions(): Permission[] {
  const authStore = useAuthStore()
  const user = authStore.user
  
  return user?.permissions || []
}

/**
 * Get user role information
 * @returns Role | null - User role information
 */
export function getUserRole(): Role | null {
  const authStore = useAuthStore()
  const user = authStore.user
  
  if (!user || !user.role_id) {
    return null
  }
  
  return {
    id: user.role_id,
    name: user.role_name || 'Unknown Role',
    description: user.role_description
  }
}

/**
 * Check if user can perform CRUD operations
 * @param resource - The resource name (e.g., 'users', 'roles', 'permissions')
 * @param operation - The operation ('create', 'read', 'update', 'delete')
 * @returns boolean - true if user can perform the operation
 */
export function canPerformOperation(resource: string, operation: 'create' | 'read' | 'update' | 'delete'): boolean {
  // Check for specific permission patterns
  const permissionPatterns = [
    `${resource}.${operation}`,
    `${resource}_${operation}`,
    `${operation}_${resource}`,
    `${operation.toUpperCase()}_${resource.toUpperCase()}`,
    `manage_${resource}`,
    `admin_${resource}`
  ]
  
  return permissionPatterns.some(pattern => hasPermissionByName(pattern))
}

/**
 * Check if user can manage a specific partner
 * @param partnerId - The partner ID to check
 * @returns boolean - true if user can manage the partner
 */
export function canManagePartner(partnerId: string | number): boolean {
  const authStore = useAuthStore()
  const user = authStore.user
  
  // Super users can manage all partners
  if (isSuperUser()) {
    return true
  }
  
  // Check if user belongs to the partner
  if (user?.partners) {
    return user.partners.some(partner => partner.id.toString() === partnerId.toString())
  }
  
  return false
}

/**
 * Directive helper for v-if conditions in templates
 * Usage: v-if="$can('permission_name')" or v-if="$hasRole('role_name')"
 */
export const permissionDirectives = {
  can: hasPermissionByName,
  hasRole: hasRoleByName,
  isSuperUser,
  canAccess: canAccessModule
}

// Export all functions for easy importing
export default {
  hasPermission,
  hasPermissionByName,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  hasRoleByName,
  hasAnyRole,
  isSuperUser,
  canAccessModule,
  getUserPermissions,
  getUserRole,
  canPerformOperation,
  canManagePartner,
  ...permissionDirectives
}
