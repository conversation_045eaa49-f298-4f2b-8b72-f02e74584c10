import CryptoJS from 'crypto-js'

/**
 * Hash utility functions matching the old app's implementation
 */

// Environment variables
const APP_KEY = import.meta.env.VITE_APP_KEY || 'QGohdFLe3w-AWjfdy7jXrZvtIMUrmPwpocQtCsRPZsF-QIuw7AKAw4'
// const APP_KEY = import.meta.env.VITE_APP_KEY || '4K7DN9O3OxZHWsapmnCkZTMvwAttZITx'

/**
 * Sort nested array for consistent hash generation
 */
const sortNestedArray = (arr: any[]): any[] => {
  return arr.map(item => {
    if (Array.isArray(item)) {
      return sortNestedArray(item)
    } else if (typeof item === 'object' && item !== null) {
      return sortNestedObject(item)
    }
    return item
  }).sort()
}

/**
 * Sort nested object for consistent hash generation
 */
const sortNestedObject = (obj: Record<string, any>): Record<string, any> => {
  const sortedObj: Record<string, any> = {}
  const sortedKeys = Object.keys(obj).sort()
  
  for (const key of sortedKeys) {
    const value = obj[key]
    if (Array.isArray(value)) {
      sortedObj[key] = sortNestedArray(value)
    } else if (typeof value === 'object' && value !== null) {
      sortedObj[key] = sortNestedObject(value)
    } else {
      sortedObj[key] = value
    }
  }
  
  return sortedObj
}

/**
 * Create hash key from request payload (matching old app's HashCreate1 function)
 */
export const createHashKey = (request: Record<string, any>): string => {
  let hashkey = ''
  
  // Sort the request object keys
  const sortedRequest = Object.keys(request).sort().reduce((obj, key) => {
    obj[key] = request[key]
    return obj
  }, {} as Record<string, any>)
  
  // Build hash string
  for (const [key, value] of Object.entries(sortedRequest)) {
    if (Array.isArray(value)) {
      const sortedValue = sortNestedArray([...value])
      sortedValue.forEach((val, index) => {
        hashkey += `&${index}=${CryptoJS.MD5(JSON.stringify(val)).toString()}`
      })
      continue
    } else if (typeof value === 'object' && value !== null) {
      const sortedValue = sortNestedObject(value)
      for (const [keyObj, valueObj] of Object.entries(sortedValue)) {
        hashkey += `&${keyObj}=${CryptoJS.MD5(JSON.stringify(valueObj)).toString()}`
      }
      continue
    }
    hashkey += `&${key}=${value}`
  }
  
  // Remove the first '&' and create final hash
  const hash = hashkey.slice(1)
  const finalHash = CryptoJS.MD5(hash + APP_KEY).toString()
  
  return finalHash
}

/**
 * Simple hash creation for basic payloads
 */
export const createSimpleHash = (payload: Record<string, any>): string => {
  // Sort payload keys for consistency
  const sortedPayload = Object.keys(payload)
    .sort()
    .reduce((result: Record<string, any>, key: string) => {
      result[key] = payload[key]
      return result
    }, {})
  
  // Create hash string
  const hashString = Object.entries(sortedPayload)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  
  // Return MD5 hash with app key
  return CryptoJS.MD5(hashString + APP_KEY).toString()
}

/**
 * Create hash for authentication requests
 */
export const createAuthHash = (payload: Record<string, any>): string => {
  return createHashKey(payload)
}

/**
 * Validate hash (for debugging purposes)
 */
export const validateHash = (payload: Record<string, any>, expectedHash: string): boolean => {
  const generatedHash = createHashKey(payload)
  return generatedHash === expectedHash
}

/**
 * Get app key
 */
export const getAppKey = (): string => {
  return APP_KEY
}

/**
 * Get auth key
 */
export const getAuthKey = (): string => {
  return import.meta.env.VITE_AUTH_KEY || '4ia2yg.Gs42I0h7(%!{FjDAt{.R5Z-'
  // return import.meta.env.VITE_AUTH_KEY || 'hjR1j33iKJ&-'
}

/**
 * Get web auth key
 */
export const getWebAuthKey = (): string => {
  return import.meta.env.VITE_WEB_AUTH_KEY || 'WEB_APP'
}

export default {
  createHashKey,
  createSimpleHash,
  createAuthHash,
  validateHash,
  getAppKey,
  getAuthKey,
  getWebAuthKey
}
