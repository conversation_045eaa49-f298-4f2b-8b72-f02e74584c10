<template>
  <div class="h-screen bg-gray-50 overflow-hidden relative">
    <!-- Sidebar -->
    <Sidebar />

    <!-- Mobile Overlay -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="sidebarStore.isMobile && sidebarStore.isOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-40"
        @click="sidebarStore.close()"
      ></div>
    </Transition>

    <!-- Main Content Area -->
    <div
      class="flex flex-col h-full sidebar-transition relative"
      :class="sidebarStore.contentMargin"
    >
      <!-- TopBar -->
      <TopBar />

      <!-- Page Content -->
      <main class="flex-1 overflow-auto bg-gray-50">
        <div class="p-4 sm:p-6">
          <!-- RouterView with transitions -->
          <RouterView v-slot="{ Component }">
            <Transition
              name="page"
              enter-active-class="transition-all duration-300 ease-out"
              enter-from-class="opacity-0 transform translate-y-4"
              enter-to-class="opacity-100 transform translate-y-0"
              leave-active-class="transition-all duration-200 ease-in"
              leave-from-class="opacity-100 transform translate-y-0"
              leave-to-class="opacity-0 transform -translate-y-4"
              mode="out-in"
            >
              <component :is="Component" />
            </Transition>
          </RouterView>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useSidebarStore } from '@/stores/sidebar'
import Sidebar from '@/components/Sidebar.vue'
import TopBar from '@/components/TopBar.vue'

const sidebarStore = useSidebarStore()

// Handle window resize for mobile detection
const handleResize = () => {
  const isMobile = window.innerWidth < 768
  sidebarStore.setMobile(isMobile)
}

// Handle escape key to close sidebar on mobile
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && sidebarStore.isMobile && sidebarStore.isOpen) {
    sidebarStore.close()
  }
}

onMounted(() => {
  // Set initial mobile state
  handleResize()

  // If initially mobile, close the sidebar
  if (window.innerWidth < 768) {
    sidebarStore.close()
  }

  window.addEventListener('resize', handleResize)
  document.addEventListener('keydown', handleKeydown)

  // Add mobile sidebar class management
  const updateBodyClass = () => {
    if (sidebarStore.isMobile && sidebarStore.isOpen) {
      document.body.classList.add('mobile-sidebar-open')
    } else {
      document.body.classList.remove('mobile-sidebar-open')
    }
  }

  // Initial body class update
  updateBodyClass()

  // Watch for sidebar state changes
  const unwatch = sidebarStore.$subscribe(() => {
    updateBodyClass()
  })

  // Cleanup function
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    document.removeEventListener('keydown', handleKeydown)
    document.body.classList.remove('mobile-sidebar-open')
    unwatch()
  })
})
</script>

<style scoped>
/* Page transition styles */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
