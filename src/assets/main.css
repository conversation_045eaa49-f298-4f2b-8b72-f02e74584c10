@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the dashboard */
@layer components {
  .sidebar-pattern {
    background-image:
      radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 2px),
      radial-gradient(circle at 75px 75px, rgba(255,255,255,0.05) 2px, transparent 2px);
    background-size: 100px 100px;
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .sidebar-item {
    @apply flex items-center px-3 py-2 rounded-lg text-slate-300 hover:text-white hover:bg-white/10 transition-all duration-200;
  }

  .sidebar-item-active {
    @apply bg-blue-600 text-white;
  }

  .submenu-item {
    @apply flex items-center px-3 py-2 rounded-lg text-slate-400 hover:text-white hover:bg-white/10 transition-all duration-200 text-sm ml-8 mt-1;
  }

  .submenu-item-active {
    @apply bg-blue-500 text-white;
  }

  .sidebar-submenu-item {
    @apply flex items-center px-3 py-2 rounded-lg text-slate-400 hover:text-white hover:bg-white/10 transition-all duration-200 text-sm ml-8 mt-1;
  }

  .sidebar-submenu-item-active {
    @apply bg-blue-500 text-white;
  }

  /* Submenu container spacing */
  .submenu-container {
    @apply pl-4 space-y-1;
  }
}

/* Mobile sidebar overlay */
.mobile-sidebar-open {
  overflow: hidden;
}

/* Smooth transitions */
.sidebar-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
