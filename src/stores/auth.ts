import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// Router will be imported dynamically to avoid circular dependency
import { authApi } from '@/services/authApi'

// Types
interface User {
  id?: number
  username?: string
  display_name?: string
  email?: string
  role_id: string
  role_name?: string // Role display name
  permissions?: Permission[]
  token?: string
  mc?: number
  clients?: Client[]
  partners?: any[] // Partner accounts
  // New fields from API response
  un?: string // User name (display name)
  cn?: string // Company name
  cid?: string // Company ID
  uid?: string // User ID
  expires?: string // Token expiration
  // OTP related fields
  requires_otp?: boolean
  otp_expires_in?: number
  otp_sent?: boolean
}

interface Permission {
  id: string | number // Can be string from API
  name: string
  description?: string
}

interface Client {
  client_id: string
  client_name: string
  client_account: string
}

interface LoginCredentials {
  username: string
  password: string
  dial_code: string
}

interface LoginWithCodeCredentials extends LoginCredentials {
  verification_code: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const permissions = ref<number[]>([])
  const role = ref<number | null>(null)
  const isSuperRole = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Partner management
  const selectedPartnerId = ref<string | null>(localStorage.getItem('selectedPartnerId'))
  const partnerList = ref<any[]>([])
  const hasMultiplePartners = computed(() => partnerList.value.length > 1)
  
  // Super roles configuration
  const superRoles = [1, 8]

  // OTP context for preserving login state
  const otpContext = ref<{
    userData?: any
    credentials?: any
    timestamp?: number
  } | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!token.value)
  const hasPermission = computed(() => (permissionId: number) =>
    permissions.value.includes(permissionId)
  )
  const isSuperUser = computed(() =>
    role.value ? superRoles.includes(role.value) : false
  )

  // Enhanced permission checking methods
  const hasPermissionByName = (permissionName: string): boolean => {
    if (isSuperUser.value) return true
    // This would need to be enhanced with actual permission name mapping
    // For now, return true for super users and false for others
    return false
  }

  const hasAnyPermission = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.some(permissionId => permissions.value.includes(permissionId))
  }

  const hasAllPermissions = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.every(permissionId => permissions.value.includes(permissionId))
  }

  const hasRole = (roleId: number): boolean => {
    return role.value === roleId
  }

  const hasAnyRole = (roleIds: number[]): boolean => {
    return role.value ? roleIds.includes(role.value) : false
  }

  // Module-based access checking
  const hasModuleAccess = (modulePermissions: number[]): boolean => {
    if (isSuperUser.value) return true
    return hasAnyPermission(modulePermissions)
  }

  // Actions
  /**
   * Authenticate user with credentials
   */
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      const payload = {
        ...credentials,
        password: btoa(credentials.password)
      }

      const response = await authApi.login(payload)

      if (response.status === 200) {
        const userData = response.message.data

        // Check if OTP verification is required
        if (userData.requires_otp) {
          return {
            success: false,
            requiresCode: true,
            message: response.message.message || 'Please verify with the OTP sent to your mobile number.',
            data: userData
          }
        }

        // Full login success - set user data
        user.value = {
          ...userData,
          username: userData.un || userData.username || '',
          id: parseInt(userData.cid || userData.uid || '0')
        }
        token.value = userData.token || ''
        role.value = parseInt(userData.role_id || '0')

        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        isSuperRole.value = superRoles.includes(role.value)

        localStorage.setItem('token', userData.token || '')
        localStorage.setItem('user', JSON.stringify(user.value))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())

        return { success: true, requiresCode: false, data: userData }
      } else if (response.status === 205 || response.status === 201 || response.status === 410) {
        const errorMsg = response.message?.message || 'Verification code required'
        return { success: false, requiresCode: true, message: errorMsg }
      } else {
        const errorMsg = response.message?.message || 'Login failed'
        error.value = errorMsg
        return { success: false, requiresCode: false, message: errorMsg }
      }
    } catch (err: any) {
      error.value = err.message || 'Login failed'
      return { success: false, requiresCode: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Authenticate user with verification code
   */
  const loginWithCode = async (credentials: LoginWithCodeCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.loginWithCode(credentials)

      if (response.status === 200) {
        const userData = response.message.data

        // Set user data with proper mapping
        user.value = {
          ...userData,
          username: userData.un || userData.username || '',
          id: parseInt(userData.cid || userData.uid || '0')
        }
        token.value = userData.token || ''
        role.value = parseInt(userData.role_id || '0')

        // Extract and set permissions
        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        // Check if super role
        isSuperRole.value = superRoles.includes(role.value)

        // Multi-client scenario handling removed as per requirements

        // Handle partner accounts if available
        if ((userData as any).partners && (userData as any).partners.length > 0) {
          partnerList.value = (userData as any).partners
          localStorage.setItem('partnerList', JSON.stringify((userData as any).partners))

          // If only one partner, auto-select it
          if ((userData as any).partners.length === 1) {
            selectedPartnerId.value = (userData as any).partners[0].id
            localStorage.setItem('selectedPartnerId', (userData as any).partners[0].id)
          } else {
            // Multiple partners - require selection
            return { success: true, requiresPartnerSelection: true, partners: (userData as any).partners }
          }
        }
        
        // Store in localStorage
        localStorage.setItem('token', userData.token || '')
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())
        
        return { success: true, requiresClientSelection: false, data: userData }
      } else {
        const errorMsg = typeof response.message === 'string' ? response.message : response.message?.message || 'Login with code failed'
        error.value = errorMsg
        return { success: false, message: errorMsg }
      }
    } catch (err: any) {
      error.value = err.message || 'Login with code failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }



  const forgotPassword = async (username: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.forgotPassword({ username, dial_code: '254' })
      
      if (response.status === 200) {
        return { success: true, message: response.message }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error.value = err.message || 'Password reset failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    // Clear state
    user.value = null
    token.value = null
    permissions.value = []
    role.value = null
    isSuperRole.value = false
    error.value = null

    // Clear localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('permissions')
    localStorage.removeItem('role')

    // Redirect to login using dynamic import
    const { router } = await import('@/router')
    router.push({ name: 'login' })
  }

  const initializeAuth = () => {
    // Restore auth state from localStorage
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    const storedPermissions = localStorage.getItem('permissions')
    const storedRole = localStorage.getItem('role')

    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)

      if (storedPermissions) {
        permissions.value = JSON.parse(storedPermissions)
      }

      if (storedRole) {
        role.value = parseInt(storedRole)
        isSuperRole.value = superRoles.includes(role.value)
      }
    }
  }

  // Helper functions
  const extractPermissionIds = (permissionsData: Permission[] | undefined): number[] => {
    if (!permissionsData || !Array.isArray(permissionsData)) {
      return []
    }
    return permissionsData.map(permission => {
      // Convert string IDs to numbers if needed
      return typeof permission.id === 'string' ? parseInt(permission.id) : permission.id
    })
  }

  const clearError = () => {
    error.value = null
  }

  /**
   * Set OTP context for preserving login state
   */
  const setOtpContext = (userData: any, credentials: any) => {
    otpContext.value = {
      userData,
      credentials,
      timestamp: Date.now()
    }
  }

  /**
   * Get OTP context if still valid (within 10 minutes)
   */
  const getOtpContext = () => {
    if (!otpContext.value) return null

    const tenMinutes = 10 * 60 * 1000
    const isExpired = Date.now() - (otpContext.value.timestamp || 0) > tenMinutes

    if (isExpired) {
      otpContext.value = null
      return null
    }

    return otpContext.value
  }

  /**
   * Clear OTP context
   */
  const clearOtpContext = () => {
    otpContext.value = null
  }

  /**
   * Select a partner account
   */
  const selectPartner = async (partnerId: string) => {
    selectedPartnerId.value = partnerId
    localStorage.setItem('selectedPartnerId', partnerId)

    // You might want to call an API to set the partner context
    // await authApi.setPartnerContext(partnerId)

    return { success: true }
  }

  /**
   * Get current selected partner
   */
  const getCurrentPartner = () => {
    if (!selectedPartnerId.value || !partnerList.value.length) return null
    return partnerList.value.find(partner => partner.id === selectedPartnerId.value)
  }

  // Initialize auth state when store is created
  initializeAuth()

  return {
    // State
    user,
    token,
    permissions,
    role,
    isSuperRole,
    isLoading,
    error,
    selectedPartnerId,
    partnerList,
    hasMultiplePartners,
    superRoles,

    // Computed
    isAuthenticated,
    hasPermission,
    isSuperUser,

    // Enhanced permission methods
    hasPermissionByName,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasModuleAccess,

    // Actions
    login,
    loginWithCode,
    forgotPassword,
    logout,
    initializeAuth,
    clearError,

    // OTP context management
    setOtpContext,
    getOtpContext,
    clearOtpContext,

    // Partner management
    selectPartner,
    getCurrentPartner
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    pick: ['token', 'user', 'permissions', 'role', 'selectedPartnerId']
  }
})
