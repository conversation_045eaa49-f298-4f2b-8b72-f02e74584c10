import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useSidebarStore = defineStore('sidebar', () => {
  // State
  const isOpen = ref(true) // Default to open on desktop
  const isMobile = ref(false)
  
  // Submenu states
  const organizationsMenuOpen = ref(false)
  const clientsMenuOpen = ref(false)

  const loansMenuOpen = ref(false)
  const billPaymentsMenuOpen = ref(false)
  const partnersMenuOpen = ref(false)
  const systemMenuOpen = ref(false)

  // Getters
  const sidebarWidth = computed(() => {
    if (isMobile.value) return isOpen.value ? 'w-64' : 'w-0'
    return isOpen.value ? 'w-64' : 'w-16'
  })

  const contentMargin = computed(() => {
    if (isMobile.value) return 'ml-0'
    return isOpen.value ? 'ml-64' : 'ml-16'
  })

  // Actions
  function toggle() {
    isOpen.value = !isOpen.value
  }

  function close() {
    isOpen.value = false
  }

  function open() {
    isOpen.value = true
  }

  function setMobile(mobile: boolean) {
    const wasDesktop = !isMobile.value
    isMobile.value = mobile

    // When switching to mobile, always close the sidebar
    if (mobile && wasDesktop) {
      close()
    }
    // When switching back to desktop, restore default open state
    else if (!mobile && wasDesktop) {
      open()
    }
  }

  function closeAllMenus() {
    organizationsMenuOpen.value = false
    clientsMenuOpen.value = false
    loansMenuOpen.value = false
    billPaymentsMenuOpen.value = false
    partnersMenuOpen.value = false
    systemMenuOpen.value = false
  }

  function toggleOrganizationsMenu() {
    if (!organizationsMenuOpen.value) closeAllMenus()
    organizationsMenuOpen.value = !organizationsMenuOpen.value
  }

  function toggleClientsMenu() {
    if (!clientsMenuOpen.value) closeAllMenus()
    clientsMenuOpen.value = !clientsMenuOpen.value
  }

  function toggleLoansMenu() {
    if (!loansMenuOpen.value) closeAllMenus()
    loansMenuOpen.value = !loansMenuOpen.value
  }

  function toggleBillPaymentsMenu() {
    if (!billPaymentsMenuOpen.value) closeAllMenus()
    billPaymentsMenuOpen.value = !billPaymentsMenuOpen.value
  }

  function togglePartnersMenu() {
    if (!partnersMenuOpen.value) closeAllMenus()
    partnersMenuOpen.value = !partnersMenuOpen.value
  }

  function toggleSystemMenu() {
    if (!systemMenuOpen.value) closeAllMenus()
    systemMenuOpen.value = !systemMenuOpen.value
  }

  function autoExpandMenus(routeName: string) {
    // Auto-expand menus based on current route
    const organizationRoutes = ['organisations', 'organisations-add', 'organisations-config', 'organisations-bulk-sms']
    const clientRoutes = ['clients', 'clients-config', 'clients-bulk']

    const loanRoutes = ['requests', 'limits', 'check-off', 'loan-accounts', 'loan-products', 'loan-repayments', 'merchants', 'merchants-config', 'merchants-bulk']
    const billPaymentRoutes = ['bill-payments', 'bill-payments-add', 'bill-payments-edit']
    const partnerRoutes = ['partners', 'partner-services', 'partners-bets', 'partners-bet-slips']
    const systemRoutes = ['system-users', 'add-user', 'edit-user', 'system-roles', 'add-role', 'edit-role', 'system-permissions', 'add-permission', 'edit-permission']

    if (organizationRoutes.includes(routeName)) {
      organizationsMenuOpen.value = true
    }
    if (clientRoutes.includes(routeName)) {
      clientsMenuOpen.value = true
    }

    if (loanRoutes.includes(routeName)) {
      loansMenuOpen.value = true
    }
    if (billPaymentRoutes.includes(routeName)) {
      billPaymentsMenuOpen.value = true
    }
    if (partnerRoutes.includes(routeName)) {
      partnersMenuOpen.value = true
    }
    if (systemRoutes.includes(routeName)) {
      systemMenuOpen.value = true
    }
  }

  return {
    // State
    isOpen,
    isMobile,
    organizationsMenuOpen,
    clientsMenuOpen,

    loansMenuOpen,
    billPaymentsMenuOpen,
    partnersMenuOpen,
    systemMenuOpen,

    // Getters
    sidebarWidth,
    contentMargin,

    // Actions
    toggle,
    close,
    open,
    setMobile,
    closeAllMenus,
    toggleOrganizationsMenu,
    toggleClientsMenu,
    toggleLoansMenu,
    toggleBillPaymentsMenu,
    togglePartnersMenu,
    toggleSystemMenu,
    autoExpandMenus
  }
}, {
  persist: {
    key: 'sidebar-store',
    storage: localStorage,
    paths: ['isOpen', 'organizationsMenuOpen', 'clientsMenuOpen', 'merchantsMenuOpen', 'loansMenuOpen', 'billPaymentsMenuOpen', 'systemMenuOpen']
  }
})
