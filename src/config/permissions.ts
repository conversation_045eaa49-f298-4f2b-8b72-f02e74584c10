/**
 * Permissions Configuration
 * Centralized permission definitions and role-based access control
 */

// Permission categories/modules
export const PERMISSION_MODULES = {
  DASHBOARD: 'dashboard',
  CLIENTS: 'clients',
  LOANS: 'loans',
  TRANSACTIONS: 'transactions',
  REPORTS: 'reports',
  SYSTEM: 'system',
  SETTINGS: 'settings'
} as const

// Permission definitions
export const PERMISSIONS = {
  // Dashboard permissions
  DASHBOARD_VIEW: { id: 1, name: 'dashboard.view', module: PERMISSION_MODULES.DASHBOARD, description: 'View dashboard' },
  DASHBOARD_ANALYTICS: { id: 2, name: 'dashboard.analytics', module: PERMISSION_MODULES.DASHBOARD, description: 'View analytics' },
  
  // Client permissions
  CLIENTS_VIEW: { id: 10, name: 'clients.view', module: PERMISSION_MODULES.CLIENTS, description: 'View clients' },
  CLIENTS_CREATE: { id: 11, name: 'clients.create', module: PERMISSION_MODULES.CLIENTS, description: 'Create clients' },
  CLIENTS_EDIT: { id: 12, name: 'clients.edit', module: PERMISSION_MODULES.CLIENTS, description: 'Edit clients' },
  CLIENTS_DELETE: { id: 13, name: 'clients.delete', module: PERMISSION_MODULES.CLIENTS, description: 'Delete clients' },
  CLIENTS_EXPORT: { id: 14, name: 'clients.export', module: PERMISSION_MODULES.CLIENTS, description: 'Export client data' },
  
  // Loan permissions
  LOANS_VIEW: { id: 20, name: 'loans.view', module: PERMISSION_MODULES.LOANS, description: 'View loans' },
  LOANS_REQUESTS_VIEW: { id: 21, name: 'loans.requests.view', module: PERMISSION_MODULES.LOANS, description: 'View loan requests' },
  LOANS_REQUESTS_APPROVE: { id: 22, name: 'loans.requests.approve', module: PERMISSION_MODULES.LOANS, description: 'Approve loan requests' },
  LOANS_REQUESTS_REJECT: { id: 23, name: 'loans.requests.reject', module: PERMISSION_MODULES.LOANS, description: 'Reject loan requests' },
  LOANS_LIMITS_VIEW: { id: 24, name: 'loans.limits.view', module: PERMISSION_MODULES.LOANS, description: 'View loan limits' },
  LOANS_LIMITS_MANAGE: { id: 25, name: 'loans.limits.manage', module: PERMISSION_MODULES.LOANS, description: 'Manage loan limits' },
  LOANS_ACCOUNTS_VIEW: { id: 26, name: 'loans.accounts.view', module: PERMISSION_MODULES.LOANS, description: 'View loan accounts' },
  LOANS_ACCOUNTS_MANAGE: { id: 27, name: 'loans.accounts.manage', module: PERMISSION_MODULES.LOANS, description: 'Manage loan accounts' },
  LOANS_REPAYMENTS_VIEW: { id: 28, name: 'loans.repayments.view', module: PERMISSION_MODULES.LOANS, description: 'View loan repayments' },
  LOANS_REPAYMENTS_VERIFY: { id: 29, name: 'loans.repayments.verify', module: PERMISSION_MODULES.LOANS, description: 'Verify loan repayments' },
  LOANS_PRODUCTS_VIEW: { id: 30, name: 'loans.products.view', module: PERMISSION_MODULES.LOANS, description: 'View loan products' },
  LOANS_PRODUCTS_MANAGE: { id: 31, name: 'loans.products.manage', module: PERMISSION_MODULES.LOANS, description: 'Manage loan products' },
  
  // Transaction permissions
  TRANSACTIONS_VIEW: { id: 40, name: 'transactions.view', module: PERMISSION_MODULES.TRANSACTIONS, description: 'View transactions' },
  TRANSACTIONS_EXPORT: { id: 41, name: 'transactions.export', module: PERMISSION_MODULES.TRANSACTIONS, description: 'Export transactions' },
  TRANSACTIONS_RECONCILE: { id: 42, name: 'transactions.reconcile', module: PERMISSION_MODULES.TRANSACTIONS, description: 'Reconcile transactions' },
  
  // Report permissions
  REPORTS_VIEW: { id: 50, name: 'reports.view', module: PERMISSION_MODULES.REPORTS, description: 'View reports' },
  REPORTS_GENERATE: { id: 51, name: 'reports.generate', module: PERMISSION_MODULES.REPORTS, description: 'Generate reports' },
  REPORTS_EXPORT: { id: 52, name: 'reports.export', module: PERMISSION_MODULES.REPORTS, description: 'Export reports' },
  REPORTS_SCHEDULE: { id: 53, name: 'reports.schedule', module: PERMISSION_MODULES.REPORTS, description: 'Schedule reports' },
  
  // System permissions
  SYSTEM_USERS_VIEW: { id: 60, name: 'system.users.view', module: PERMISSION_MODULES.SYSTEM, description: 'View system users' },
  SYSTEM_USERS_CREATE: { id: 61, name: 'system.users.create', module: PERMISSION_MODULES.SYSTEM, description: 'Create system users' },
  SYSTEM_USERS_EDIT: { id: 62, name: 'system.users.edit', module: PERMISSION_MODULES.SYSTEM, description: 'Edit system users' },
  SYSTEM_USERS_DELETE: { id: 63, name: 'system.users.delete', module: PERMISSION_MODULES.SYSTEM, description: 'Delete system users' },
  SYSTEM_ROLES_VIEW: { id: 64, name: 'system.roles.view', module: PERMISSION_MODULES.SYSTEM, description: 'View system roles' },
  SYSTEM_ROLES_CREATE: { id: 65, name: 'system.roles.create', module: PERMISSION_MODULES.SYSTEM, description: 'Create system roles' },
  SYSTEM_ROLES_EDIT: { id: 66, name: 'system.roles.edit', module: PERMISSION_MODULES.SYSTEM, description: 'Edit system roles' },
  SYSTEM_ROLES_DELETE: { id: 67, name: 'system.roles.delete', module: PERMISSION_MODULES.SYSTEM, description: 'Delete system roles' },
  SYSTEM_PERMISSIONS_VIEW: { id: 68, name: 'system.permissions.view', module: PERMISSION_MODULES.SYSTEM, description: 'View permissions' },
  SYSTEM_AUDIT_VIEW: { id: 69, name: 'system.audit.view', module: PERMISSION_MODULES.SYSTEM, description: 'View audit logs' },
  
  // Settings permissions
  SETTINGS_VIEW: { id: 70, name: 'settings.view', module: PERMISSION_MODULES.SETTINGS, description: 'View settings' },
  SETTINGS_EDIT: { id: 71, name: 'settings.edit', module: PERMISSION_MODULES.SETTINGS, description: 'Edit settings' },
  SETTINGS_SYSTEM: { id: 72, name: 'settings.system', module: PERMISSION_MODULES.SETTINGS, description: 'Manage system settings' }
} as const

// Role definitions with their default permissions
export const ROLES = {
  SUPER_ADMIN: {
    id: 1,
    name: 'Super Admin',
    description: 'Full system access',
    permissions: Object.values(PERMISSIONS).map(p => p.id)
  },
  ADMIN: {
    id: 2,
    name: 'Administrator',
    description: 'Administrative access',
    permissions: [
      PERMISSIONS.DASHBOARD_VIEW.id,
      PERMISSIONS.DASHBOARD_ANALYTICS.id,
      PERMISSIONS.CLIENTS_VIEW.id,
      PERMISSIONS.CLIENTS_CREATE.id,
      PERMISSIONS.CLIENTS_EDIT.id,
      PERMISSIONS.CLIENTS_EXPORT.id,
      PERMISSIONS.LOANS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_APPROVE.id,
      PERMISSIONS.LOANS_REQUESTS_REJECT.id,
      PERMISSIONS.LOANS_LIMITS_VIEW.id,
      PERMISSIONS.LOANS_LIMITS_MANAGE.id,
      PERMISSIONS.LOANS_ACCOUNTS_VIEW.id,
      PERMISSIONS.LOANS_ACCOUNTS_MANAGE.id,
      PERMISSIONS.LOANS_REPAYMENTS_VIEW.id,
      PERMISSIONS.LOANS_REPAYMENTS_VERIFY.id,
      PERMISSIONS.LOANS_PRODUCTS_VIEW.id,
      PERMISSIONS.LOANS_PRODUCTS_MANAGE.id,
      PERMISSIONS.TRANSACTIONS_VIEW.id,
      PERMISSIONS.TRANSACTIONS_EXPORT.id,
      PERMISSIONS.TRANSACTIONS_RECONCILE.id,
      PERMISSIONS.REPORTS_VIEW.id,
      PERMISSIONS.REPORTS_GENERATE.id,
      PERMISSIONS.REPORTS_EXPORT.id,
      PERMISSIONS.SYSTEM_USERS_VIEW.id,
      PERMISSIONS.SYSTEM_USERS_CREATE.id,
      PERMISSIONS.SYSTEM_USERS_EDIT.id,
      PERMISSIONS.SYSTEM_ROLES_VIEW.id,
      PERMISSIONS.SETTINGS_VIEW.id,
      PERMISSIONS.SETTINGS_EDIT.id
    ]
  },
  MANAGER: {
    id: 3,
    name: 'Manager',
    description: 'Management access',
    permissions: [
      PERMISSIONS.DASHBOARD_VIEW.id,
      PERMISSIONS.DASHBOARD_ANALYTICS.id,
      PERMISSIONS.CLIENTS_VIEW.id,
      PERMISSIONS.CLIENTS_EDIT.id,
      PERMISSIONS.CLIENTS_EXPORT.id,
      PERMISSIONS.LOANS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_APPROVE.id,
      PERMISSIONS.LOANS_REQUESTS_REJECT.id,
      PERMISSIONS.LOANS_LIMITS_VIEW.id,
      PERMISSIONS.LOANS_LIMITS_MANAGE.id,
      PERMISSIONS.LOANS_ACCOUNTS_VIEW.id,
      PERMISSIONS.LOANS_REPAYMENTS_VIEW.id,
      PERMISSIONS.LOANS_REPAYMENTS_VERIFY.id,
      PERMISSIONS.LOANS_PRODUCTS_VIEW.id,
      PERMISSIONS.TRANSACTIONS_VIEW.id,
      PERMISSIONS.TRANSACTIONS_EXPORT.id,
      PERMISSIONS.REPORTS_VIEW.id,
      PERMISSIONS.REPORTS_GENERATE.id,
      PERMISSIONS.REPORTS_EXPORT.id,
      PERMISSIONS.SETTINGS_VIEW.id
    ]
  },
  OPERATOR: {
    id: 4,
    name: 'Operator',
    description: 'Operational access',
    permissions: [
      PERMISSIONS.DASHBOARD_VIEW.id,
      PERMISSIONS.CLIENTS_VIEW.id,
      PERMISSIONS.LOANS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_VIEW.id,
      PERMISSIONS.LOANS_LIMITS_VIEW.id,
      PERMISSIONS.LOANS_ACCOUNTS_VIEW.id,
      PERMISSIONS.LOANS_REPAYMENTS_VIEW.id,
      PERMISSIONS.LOANS_PRODUCTS_VIEW.id,
      PERMISSIONS.TRANSACTIONS_VIEW.id,
      PERMISSIONS.REPORTS_VIEW.id
    ]
  },
  VIEWER: {
    id: 5,
    name: 'Viewer',
    description: 'Read-only access',
    permissions: [
      PERMISSIONS.DASHBOARD_VIEW.id,
      PERMISSIONS.CLIENTS_VIEW.id,
      PERMISSIONS.LOANS_VIEW.id,
      PERMISSIONS.LOANS_REQUESTS_VIEW.id,
      PERMISSIONS.LOANS_LIMITS_VIEW.id,
      PERMISSIONS.LOANS_ACCOUNTS_VIEW.id,
      PERMISSIONS.LOANS_REPAYMENTS_VIEW.id,
      PERMISSIONS.LOANS_PRODUCTS_VIEW.id,
      PERMISSIONS.TRANSACTIONS_VIEW.id,
      PERMISSIONS.REPORTS_VIEW.id
    ]
  }
} as const

// Helper functions
export const getPermissionsByModule = (module: string) => {
  return Object.values(PERMISSIONS).filter(permission => permission.module === module)
}

export const getPermissionById = (id: number) => {
  return Object.values(PERMISSIONS).find(permission => permission.id === id)
}

export const getPermissionByName = (name: string) => {
  return Object.values(PERMISSIONS).find(permission => permission.name === name)
}

export const getRoleById = (id: number) => {
  return Object.values(ROLES).find(role => role.id === id)
}

export const hasPermission = (userPermissions: number[], permissionId: number): boolean => {
  return userPermissions.includes(permissionId)
}

export const hasAnyPermission = (userPermissions: number[], permissionIds: number[]): boolean => {
  return permissionIds.some(id => userPermissions.includes(id))
}

export const hasAllPermissions = (userPermissions: number[], permissionIds: number[]): boolean => {
  return permissionIds.every(id => userPermissions.includes(id))
}

export const hasModuleAccess = (userPermissions: number[], module: string): boolean => {
  const modulePermissions = getPermissionsByModule(module)
  return modulePermissions.some(permission => userPermissions.includes(permission.id))
}

// Route-based permission mapping
export const ROUTE_PERMISSIONS = {
  '/dashboard': [PERMISSIONS.DASHBOARD_VIEW.id],
  '/clients': [PERMISSIONS.CLIENTS_VIEW.id],
  '/clients/add': [PERMISSIONS.CLIENTS_CREATE.id],
  '/loans': [PERMISSIONS.LOANS_VIEW.id],
  '/loans/requests': [PERMISSIONS.LOANS_REQUESTS_VIEW.id],
  '/loans/limits': [PERMISSIONS.LOANS_LIMITS_VIEW.id],
  '/loans/accounts': [PERMISSIONS.LOANS_ACCOUNTS_VIEW.id],
  '/loans/repayments': [PERMISSIONS.LOANS_REPAYMENTS_VIEW.id],
  '/loans/products': [PERMISSIONS.LOANS_PRODUCTS_VIEW.id],
  '/transactions': [PERMISSIONS.TRANSACTIONS_VIEW.id],
  '/reports': [PERMISSIONS.REPORTS_VIEW.id],
  '/system/users': [PERMISSIONS.SYSTEM_USERS_VIEW.id],
  '/system/roles': [PERMISSIONS.SYSTEM_ROLES_VIEW.id],
  '/settings': [PERMISSIONS.SETTINGS_VIEW.id]
} as const

export type PermissionId = typeof PERMISSIONS[keyof typeof PERMISSIONS]['id']
export type RoleId = typeof ROLES[keyof typeof ROLES]['id']
export type ModuleName = typeof PERMISSION_MODULES[keyof typeof PERMISSION_MODULES]
