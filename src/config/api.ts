/**
 * API Configuration Management
 * Provides centralized configuration for API endpoints and environments
 */

export interface ApiEnvironment {
  name: string
  displayName: string
  baseUrl: string
  description: string
}

/**
 * Available API environments
 */
export const API_ENVIRONMENTS: Record<string, ApiEnvironment> = {
  development: {
    name: 'development',
    displayName: 'Development',
    baseUrl: 'https://b2b.mb.mb-gaming.life',
    description: 'Development environment for testing'
  },
  production: {
    name: 'production',
    displayName: 'Production',
    baseUrl: 'https://b2b.mb.mb-gaming.life',
    // baseUrl: 'https://dash.api.dev.mossbets.bet',
    description: 'Production environment'
  }
}

/**
 * Get current API environment from environment variables
 */
export const getCurrentApiEnvironment = (): string => {
  return import.meta.env.VITE_API_ENVIRONMENT || 'development'
}

/**
 * Get API base URL based on current environment
 */
export const getApiBaseUrl = (): string => {
  // In development mode, use proxy to avoid CORS issues
  if (import.meta.env.DEV) {
    return '/api/'
  }

  // Check for explicit base URL override
  const explicitBaseUrl = import.meta.env.VITE_API_BASE_URL
  if (explicitBaseUrl) {
    return explicitBaseUrl.endsWith('/') ? explicitBaseUrl : `${explicitBaseUrl}/`
  }

  // Fallback to environment-based URL
  const currentEnv = getCurrentApiEnvironment()
  const environment = API_ENVIRONMENTS[currentEnv]
  
  if (!environment) {
    console.warn(`Unknown API environment: ${currentEnv}, falling back to development`)
    return API_ENVIRONMENTS.development.baseUrl + '/'
  }

  return environment.baseUrl + '/'
}

/**
 * Get API timeout from environment variables
 */
export const getApiTimeout = (): number => {
  const timeout = import.meta.env.VITE_API_TIMEOUT
  return timeout ? parseInt(timeout, 10) : 30000
}

/**
 * Check if API debugging is enabled
 */
export const isApiDebugEnabled = (): boolean => {
  return import.meta.env.VITE_DEBUG_API === 'true' || import.meta.env.DEV
}

/**
 * Get current API environment configuration
 */
export const getCurrentApiConfig = (): ApiEnvironment & { timeout: number; debug: boolean } => {
  const currentEnv = getCurrentApiEnvironment()
  const environment = API_ENVIRONMENTS[currentEnv] || API_ENVIRONMENTS.development
  
  return {
    ...environment,
    baseUrl: getApiBaseUrl(),
    timeout: getApiTimeout(),
    debug: isApiDebugEnabled()
  }
}


