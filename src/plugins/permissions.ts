import type { App } from 'vue'
import { permissionDirectives } from '@/utils/permissions'

/**
 * Vue Plugin for Permission Utilities
 * 
 * This plugin makes permission checking functions available globally
 * in Vue components through the $permissions object.
 */

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $can: (permission: string) => boolean
    $hasRole: (role: string) => boolean
    $isSuperUser: () => boolean
    $canAccess: (module: string) => boolean
  }
}

export default {
  install(app: App) {
    // Add global properties
    app.config.globalProperties.$can = permissionDirectives.can
    app.config.globalProperties.$hasRole = permissionDirectives.hasRole
    app.config.globalProperties.$isSuperUser = permissionDirectives.isSuperUser
    app.config.globalProperties.$canAccess = permissionDirectives.canAccess
    
    // Add provide/inject for composition API
    app.provide('permissions', permissionDirectives)
  }
}
