<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Bets Management</h1>
        <p class="mt-1 text-sm text-gray-500">Monitor and manage partner betting activity</p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button
          @click="exportBets"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          Export
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Partner</label>
          <select
            v-model="filters.partner_id"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Partners</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.status"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="0">Pending</option>
            <option value="1">Won</option>
            <option value="2">Lost</option>
            <option value="3">Cancelled</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Bet Type</label>
          <select
            v-model="filters.bet_type"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Types</option>
            <option value="0">Single</option>
            <option value="1">Multiple</option>
            <option value="2">System</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
          <input
            v-model="filters.start"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
          <input
            v-model="filters.end"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div class="flex items-end">
          <button
            @click="loadBets"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Filter
          </button>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-blue-100">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Bets</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(summary.total_bets) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-green-100">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Stake</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(summary.total_stake) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-purple-100">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Potential Win</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(summary.potential_win) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-orange-100">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Actual Win</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(summary.actual_win) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Bets Table -->
    <DataTable
      :data="bets"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="pagination.current_page"
      :total-records="pagination.total"
      :page-size="pagination.limit"
      :has-actions="true"
      title="Bets"
      @page-change="handlePageChange"
      @limit-change="handleLimitChange"
    >
      <!-- Bet Info -->
      <template #cell-bet_info="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ item.bet_reference }}</div>
          <div class="text-gray-500">ID: {{ item.bet_id }}</div>
        </div>
      </template>

      <!-- Partner -->
      <template #cell-partner="{ item }">
        <div class="text-sm font-medium text-gray-900">
          {{ getPartnerName(item.partner_id) }}
        </div>
      </template>

      <!-- Bet Type -->
      <template #cell-bet_type="{ item }">
        <span
          :class="[
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            getBetTypeClass(item.bet_type)
          ]"
        >
          {{ getBetTypeName(item.bet_type) }}
        </span>
      </template>

      <!-- Amount -->
      <template #cell-bet_amount="{ item }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(item.bet_amount) }}
        </div>
      </template>

      <!-- Possible Win -->
      <template #cell-possible_win="{ item }">
        <div class="text-sm font-medium text-green-600">
          {{ formatCurrency(item.possible_win) }}
        </div>
      </template>

      <!-- Total Odd -->
      <template #cell-total_odd="{ item }">
        <div class="text-sm font-medium text-blue-600">
          {{ item.total_odd ? parseFloat(item.total_odd).toFixed(2) : 'N/A' }}
        </div>
      </template>

      <!-- Status -->
      <template #cell-status="{ item }">
        <span
          :class="[
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            getBetStatusClass(item.status)
          ]"
        >
          {{ getBetStatusName(item.status) }}
        </span>
      </template>

      <!-- Created Date -->
      <template #cell-created_at="{ item }">
        <div class="text-sm text-gray-500">
          {{ formatDate(item.created_at, 'datetime') }}
        </div>
      </template>

      <!-- Actions -->
      <template #actions="{ item }">
        <button
          @click="viewBetDetails(item)"
          class="text-blue-600 hover:text-blue-900 text-sm font-medium mr-3"
        >
          View Details
        </button>
        <button
          @click="viewBetSlips(item)"
          class="text-green-600 hover:text-green-900 text-sm font-medium"
        >
          View Slips
        </button>
      </template>
    </DataTable>

    <!-- Bet Details Modal -->
    <BetDetailsModal
      :is-open="showDetailsModal"
      :bet="selectedBet"
      @close="closeDetailsModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { betsApi, type Bet } from '@/services/betsApi'
import { partnerApi, type Partner } from '@/services/partnerApi'
import { formatCurrency, formatDate } from '@/utils/formatters'
import DataTable from '@/components/DataTable.vue'
import BetDetailsModal from '@/components/Modals/BetDetailsModal.vue'

// Reactive data
const loading = ref(false)
const bets = ref<Bet[]>([])
const partners = ref<Partner[]>([])
const selectedBet = ref<Bet | null>(null)
const showDetailsModal = ref(false)

const filters = reactive({
  partner_id: '',
  status: '',
  bet_type: '',
  start: '',
  end: ''
})

const pagination = reactive({
  current_page: 1,
  limit: 10,
  total: 0
})

// Table headers
const tableHeaders = {
  bet_info: 'Bet Reference',
  partner: 'Partner',
  bet_type: 'Type',
  bet_amount: 'Stake',
  possible_win: 'Possible Win',
  total_odd: 'Total Odds',
  status: 'Status',
  created_at: 'Date'
}

// Computed summary
const summary = computed(() => {
  return {
    total_bets: bets.value.length,
    total_stake: bets.value.reduce((sum, bet) => sum + (bet.bet_amount || 0), 0),
    potential_win: bets.value.reduce((sum, bet) => sum + (bet.potential_win || 0), 0),
    actual_win: bets.value.reduce((sum, bet) => sum + (bet.actual_win || 0), 0)
  }
})

// Methods
const loadBets = async () => {
  loading.value = true
  try {
    const response = await betsApi.getPartnerBets({
      page: pagination.current_page,
      limit: pagination.limit,
      partner_id: filters.partner_id,
      status: filters.status,
      bet_type: filters.bet_type,
      start: filters.start,
      end: filters.end
    })
    
    if (response.status === 200) {
      bets.value = response.message.data || []
      pagination.total = response.message.total || 0
    }
  } catch (error) {
    console.error('Failed to load bets:', error)
  } finally {
    loading.value = false
  }
}

const loadPartners = async () => {
  try {
    const response = await partnerApi.getPartners({ limit: 100 })
    if (response.status === 200) {
      partners.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load partners:', error)
  }
}

const getPartnerName = (partnerId: number): string => {
  const partner = partners.value.find(p => p.id === partnerId)
  return partner?.name || `Partner ${partnerId}`
}

const getBetTypeName = (type: number): string => {
  switch (type) {
    case 0: return 'Single'
    case 1: return 'Multiple'
    case 2: return 'System'
    default: return 'Unknown'
  }
}

const getBetTypeClass = (type: number): string => {
  switch (type) {
    case 0: return 'bg-blue-100 text-blue-800'
    case 1: return 'bg-purple-100 text-purple-800'
    case 2: return 'bg-orange-100 text-orange-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getBetStatusName = (status: number): string => {
  switch (status) {
    case 0: return 'Pending'
    case 1: return 'Won'
    case 2: return 'Lost'
    case 3: return 'Cancelled'
    default: return 'Unknown'
  }
}

const getBetStatusClass = (status: number): string => {
  switch (status) {
    case 0: return 'bg-yellow-100 text-yellow-800'
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-red-100 text-red-800'
    case 3: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num)
}

const viewBetDetails = (bet: Bet) => {
  selectedBet.value = bet
  showDetailsModal.value = true
}

const viewBetSlips = (bet: Bet) => {
  // Navigate to bet slips page
  console.log('View bet slips for:', bet)
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedBet.value = null
}

const exportBets = () => {
  // TODO: Implement export functionality
  console.log('Export bets with filters:', filters)
}

const handlePageChange = (page: number) => {
  pagination.current_page = page
  loadBets()
}

const handleLimitChange = (limit: number) => {
  pagination.limit = limit
  pagination.current_page = 1
  loadBets()
}

// Watch filters for auto-refresh
watch(filters, () => {
  pagination.current_page = 1
  loadBets()
}, { deep: true })

// Lifecycle
onMounted(async () => {
  await Promise.all([loadBets(), loadPartners()])
})
</script>
