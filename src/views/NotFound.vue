<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Logo -->
      <div class="mx-auto h-20 w-auto flex items-center justify-center">
        <img src="@/assets/logo.png" alt="Mossbets B2B Logo" class="h-20 w-auto" />
      </div>

      <!-- 404 Content -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <!-- 404 Icon -->
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
          <svg class="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>

        <!-- Error Message -->
        <div class="space-y-4">
          <h1 class="text-4xl font-bold text-gray-900">404</h1>
          <h2 class="text-xl font-semibold text-gray-700">Page Not Found</h2>
          <p class="text-gray-600">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 space-y-3">
          <button
            @click="goBack"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Go Back
          </button>
          
          <router-link
            :to="{ name: 'dashboard' }"
            class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Go to Dashboard
          </router-link>
        </div>

        <!-- Help Text -->
        <div class="mt-6 text-xs text-gray-500">
          If you believe this is an error, please contact support.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // If no history, go to dashboard
    router.push({ name: 'dashboard' })
  }
}
</script>
