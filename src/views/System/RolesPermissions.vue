<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Roles & Permissions</h1>
        <p class="mt-1 text-sm text-gray-500">Manage user roles and permissions</p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button
          @click="showCreateRoleModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Role
        </button>
        <button
          @click="showCreatePermissionModal = true"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Permission
        </button>
      </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'roles'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'roles'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          Roles ({{ roles.length }})
        </button>
        <button
          @click="activeTab = 'permissions'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'permissions'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          Permissions ({{ permissions.length }})
        </button>
      </nav>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Roles Tab -->
    <div v-else-if="activeTab === 'roles'" class="space-y-6">
      <!-- Search and Filter -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="relative">
          <input
            v-model="roleSearch"
            type="text"
            placeholder="Search roles..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- Roles Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        <div
          v-for="role in filteredRoles"
          :key="role.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
        >
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-medium text-gray-900 truncate">{{ role.name }}</h3>
                <p class="mt-1 text-sm text-gray-500 line-clamp-2">{{ role.description || 'No description' }}</p>
              </div>
              <div class="ml-3 flex-shrink-0">
                <div class="relative">
                  <button
                    @click="toggleRoleDropdown(role.id)"
                    class="p-1 rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                    </svg>
                  </button>
                  
                  <!-- Dropdown Menu -->
                  <div
                    v-if="activeRoleDropdown === role.id"
                    class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                  >
                    <div class="py-1">
                      <button
                        @click="editRole(role)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Edit Role
                      </button>
                      <button
                        @click="showAddPermissionToRoleModal(role)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Add Permission
                      </button>
                      <button
                        @click="showAddUserToRoleModal(role)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Add to User
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="mt-4">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-500">Permissions</span>
                <span class="font-medium text-gray-900">{{ role.permissions?.length || 0 }}</span>
              </div>
              
              <!-- Permission Tags -->
              <div v-if="role.permissions?.length" class="mt-2 flex flex-wrap gap-1">
                <span
                  v-for="permission in role.permissions.slice(0, 3)"
                  :key="permission.id"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ permission.name }}
                </span>
                <span
                  v-if="role.permissions.length > 3"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
                >
                  +{{ role.permissions.length - 3 }} more
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Permissions Tab -->
    <div v-else-if="activeTab === 'permissions'" class="space-y-6">
      <!-- Search and Filter -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div class="relative">
          <input
            v-model="permissionSearch"
            type="text"
            placeholder="Search permissions..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
        
        <!-- Module Filter -->
        <select
          v-model="selectedModule"
          class="block w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Modules</option>
          <option v-for="module in modules" :key="module" :value="module">{{ module }}</option>
        </select>
      </div>

      <!-- Permissions Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        <div
          v-for="permission in filteredPermissions"
          :key="permission.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
        >
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-medium text-gray-900 truncate">{{ permission.name }}</h3>
                <p class="mt-1 text-sm text-gray-500 line-clamp-2">{{ permission.description || 'No description' }}</p>
                <div v-if="permission.module" class="mt-2">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {{ permission.module }}
                  </span>
                </div>
              </div>
              <div class="ml-3 flex-shrink-0">
                <div class="relative">
                  <button
                    @click="togglePermissionDropdown(permission.id)"
                    class="p-1 rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                    </svg>
                  </button>
                  
                  <!-- Dropdown Menu -->
                  <div
                    v-if="activePermissionDropdown === permission.id"
                    class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                  >
                    <div class="py-1">
                      <button
                        @click="editPermission(permission)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Edit Permission
                      </button>
                      <button
                        @click="showAddPermissionToUserModal(permission)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Add to User
                      </button>
                      <button
                        @click="showAddPermissionToRoleModal(null, permission)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Add to Role
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Permission Assignment Modal -->
    <PermissionAssignmentModal
      :is-open="assignmentModal.isOpen"
      :type="assignmentModal.type"
      :target-id="assignmentModal.targetId"
      :target-name="assignmentModal.targetName"
      @close="closeAssignmentModal"
      @assigned="handleAssignment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { systemApi, type Role, type Permission } from '@/services/systemApi'
import PermissionAssignmentModal from '@/components/Modals/PermissionAssignmentModal.vue'

// Reactive data
const loading = ref(false)
const activeTab = ref<'roles' | 'permissions'>('roles')
const roles = ref<Role[]>([])
const permissions = ref<Permission[]>([])
const roleSearch = ref('')
const permissionSearch = ref('')
const selectedModule = ref('')
const activeRoleDropdown = ref<number | null>(null)
const activePermissionDropdown = ref<number | null>(null)

// Modal states
const showCreateRoleModal = ref(false)
const showCreatePermissionModal = ref(false)
const assignmentModal = ref({
  isOpen: false,
  type: 'permission-to-user' as 'permission-to-user' | 'permission-to-role' | 'user-to-role' | 'role-to-user',
  targetId: null as number | null,
  targetName: ''
})

// Computed properties
const filteredRoles = computed(() => {
  return roles.value.filter(role =>
    role.name.toLowerCase().includes(roleSearch.value.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(roleSearch.value.toLowerCase()))
  )
})

const filteredPermissions = computed(() => {
  return permissions.value.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(permissionSearch.value.toLowerCase()) ||
      (permission.description && permission.description.toLowerCase().includes(permissionSearch.value.toLowerCase()))
    
    const matchesModule = !selectedModule.value || permission.module === selectedModule.value
    
    return matchesSearch && matchesModule
  })
})

const modules = computed(() => {
  const moduleSet = new Set(permissions.value.map(p => p.module).filter(Boolean))
  return Array.from(moduleSet).sort()
})

// Methods
const loadRoles = async () => {
  try {
    const response = await systemApi.getRoles()
    if (response.status === 200) {
      roles.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load roles:', error)
  }
}

const loadPermissions = async () => {
  try {
    const response = await systemApi.getPermissions()
    if (response.status === 200) {
      permissions.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load permissions:', error)
  }
}

const toggleRoleDropdown = (roleId: number) => {
  activeRoleDropdown.value = activeRoleDropdown.value === roleId ? null : roleId
  activePermissionDropdown.value = null
}

const togglePermissionDropdown = (permissionId: number) => {
  activePermissionDropdown.value = activePermissionDropdown.value === permissionId ? null : permissionId
  activeRoleDropdown.value = null
}

const editRole = (role: Role) => {
  // TODO: Implement edit role modal
  console.log('Edit role:', role)
  activeRoleDropdown.value = null
}

const editPermission = (permission: Permission) => {
  // TODO: Implement edit permission modal
  console.log('Edit permission:', permission)
  activePermissionDropdown.value = null
}

const showAddPermissionToRoleModal = (role?: Role, permission?: Permission) => {
  if (role) {
    assignmentModal.value = {
      isOpen: true,
      type: 'permission-to-role',
      targetId: role.id,
      targetName: role.name
    }
  } else if (permission) {
    assignmentModal.value = {
      isOpen: true,
      type: 'permission-to-role',
      targetId: null,
      targetName: permission.name
    }
  }
  activeRoleDropdown.value = null
  activePermissionDropdown.value = null
}

const showAddUserToRoleModal = (role: Role) => {
  assignmentModal.value = {
    isOpen: true,
    type: 'user-to-role',
    targetId: role.id,
    targetName: role.name
  }
  activeRoleDropdown.value = null
}

const showAddPermissionToUserModal = (permission: Permission) => {
  assignmentModal.value = {
    isOpen: true,
    type: 'permission-to-user',
    targetId: permission.id,
    targetName: permission.name
  }
  activePermissionDropdown.value = null
}

const closeAssignmentModal = () => {
  assignmentModal.value.isOpen = false
}

const handleAssignment = async (assignedItems: any[]) => {
  console.log('Assigned items:', assignedItems)
  console.log('Assignment type:', assignmentModal.value.type)
  console.log('Target:', assignmentModal.value.targetId, assignmentModal.value.targetName)

  // Here you would call the appropriate API to actually assign the items
  // For example:
  // if (assignmentModal.value.type === 'permission-to-role') {
  //   await systemApi.assignPermissionsToRole(assignmentModal.value.targetId, assignedItems.map(item => item.id))
  // }

  // Refresh the data
  await Promise.all([loadRoles(), loadPermissions()])
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([loadRoles(), loadPermissions()])
  } finally {
    loading.value = false
  }
})

// Close dropdowns when clicking outside
import { onClickOutside } from '@vueuse/core'
import { nextTick } from 'vue'

// Close dropdowns on outside click
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    activeRoleDropdown.value = null
    activePermissionDropdown.value = null
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
