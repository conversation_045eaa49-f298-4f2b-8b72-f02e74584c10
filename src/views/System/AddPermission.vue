<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Add New Permission</h1>
          <p class="text-gray-600 mt-1">Create a new system permission for access control</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Permissions
        </button>
      </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="savePermission" class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Permission Name *</label>
              <input
                v-model="permissionForm.name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="e.g., users.view, loans.approve"
              />
              <p class="mt-1 text-sm text-gray-500">Use dot notation for hierarchical permissions</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Module *</label>
              <select
                v-model="permissionForm.module"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Select Module</option>
                <option v-for="module in modules" :key="module" :value="module">
                  {{ formatModuleName(module) }}
                </option>
              </select>
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                v-model="permissionForm.description"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Describe what this permission allows users to do"
              ></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="permissionForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option :value="1">Active</option>
                <option :value="0">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Permission Templates -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permission Templates</h3>
          <p class="text-sm text-gray-600 mb-4">Quick templates for common permission patterns</p>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div 
              v-for="template in permissionTemplates" 
              :key="template.action"
              @click="applyTemplate(template)"
              class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
            >
              <div class="flex items-center">
                <component :is="template.icon" class="w-5 h-5 text-blue-600 mr-2" />
                <h4 class="font-medium text-gray-900">{{ template.label }}</h4>
              </div>
              <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
              <div class="mt-2">
                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ template.pattern }}</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Permission Preview -->
        <div v-if="permissionForm.name && permissionForm.module">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permission Preview</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-start space-x-4">
              <ShieldCheckIcon class="w-8 h-8 text-blue-600 mt-1" />
              <div class="flex-1">
                <h4 class="font-medium text-gray-900">{{ permissionForm.name }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ permissionForm.description || 'No description provided' }}</p>
                <div class="flex items-center mt-2 space-x-4">
                  <span class="text-xs text-gray-500">Module: <span class="font-medium">{{ formatModuleName(permissionForm.module) }}</span></span>
                  <span :class="getStatusClass(permissionForm.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ getStatusText(permissionForm.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading || !permissionForm.name || !permissionForm.module"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Creating...' : 'Create Permission' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeftIcon,
  ShieldCheckIcon,
  EyeIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  DocumentArrowDownIcon
} from '@heroicons/vue/24/outline'
import { PERMISSION_MODULES } from '@/config/permissions'
import { systemApi } from '@/services/systemApi'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)

// Permission form
const permissionForm = ref({
  name: '',
  description: '',
  module: '',
  status: 1
})

// Available modules
const modules = Object.values(PERMISSION_MODULES)

// Permission templates
const permissionTemplates = [
  {
    action: 'view',
    label: 'View',
    description: 'Read-only access',
    pattern: 'module.view',
    icon: EyeIcon
  },
  {
    action: 'create',
    label: 'Create',
    description: 'Create new records',
    pattern: 'module.create',
    icon: PlusIcon
  },
  {
    action: 'edit',
    label: 'Edit',
    description: 'Modify existing records',
    pattern: 'module.edit',
    icon: PencilIcon
  },
  {
    action: 'delete',
    label: 'Delete',
    description: 'Remove records',
    pattern: 'module.delete',
    icon: TrashIcon
  },
  {
    action: 'approve',
    label: 'Approve',
    description: 'Approve requests',
    pattern: 'module.approve',
    icon: CheckIcon
  },
  {
    action: 'export',
    label: 'Export',
    description: 'Export data',
    pattern: 'module.export',
    icon: DocumentArrowDownIcon
  }
]

// Methods
const applyTemplate = (template: typeof permissionTemplates[0]) => {
  if (permissionForm.value.module) {
    permissionForm.value.name = `${permissionForm.value.module}.${template.action}`
    permissionForm.value.description = `${template.description} for ${formatModuleName(permissionForm.value.module)}`
  } else {
    permissionForm.value.name = template.pattern
    permissionForm.value.description = template.description
  }
}

const savePermission = async () => {
  if (!permissionForm.value.name.trim()) {
    alert('Permission name is required')
    return
  }

  if (!confirm('Are you sure you want to create this permission?')) {
    return
  }

  loading.value = true
  try {
    const response = await systemApi.createPermission({
      name: permissionForm.value.name.trim(),
      description: permissionForm.value.description.trim(),
      module: permissionForm.value.module,
      status: permissionForm.value.status
    })

    if (response.status === 200) {
      alert('Permission created successfully!')
      router.push({ name: 'system-permissions' })
    } else {
      alert('Error creating permission: ' + response.message)
    }
  } catch (error) {
    console.error('Error creating permission:', error)
    alert('Error creating permission')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-permissions' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Active',
    0: 'Inactive'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    0: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}
</script>
