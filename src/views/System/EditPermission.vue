<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Edit Permission</h1>
          <p class="text-gray-600 mt-1">Update permission information and settings</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Permissions
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-2 text-sm text-gray-500">Loading permission data...</p>
    </div>

    <!-- Form -->
    <div v-else class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="savePermission" class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Permission Name *</label>
              <input
                v-model="permissionForm.name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="e.g., users.view, loans.approve"
              />
              <p class="mt-1 text-sm text-gray-500">Use dot notation for hierarchical permissions</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Module *</label>
              <select
                v-model="permissionForm.module"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Select Module</option>
                <option v-for="module in modules" :key="module" :value="module">
                  {{ formatModuleName(module) }}
                </option>
              </select>
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                v-model="permissionForm.description"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Describe what this permission allows users to do"
              ></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="permissionForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option :value="1">Active</option>
                <option :value="0">Inactive</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Permission ID</label>
              <input
                :value="permissionForm.id"
                type="text"
                disabled
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"
              />
              <p class="mt-1 text-sm text-gray-500">System-generated ID (read-only)</p>
            </div>
          </div>
        </div>

        <!-- Permission Usage -->
        <div v-if="permissionUsage">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permission Usage</h3>
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <InformationCircleIcon class="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 class="text-sm font-medium text-blue-900">Usage Information</h4>
                <div class="mt-2 text-sm text-blue-800">
                  <p>This permission is currently assigned to:</p>
                  <ul class="mt-1 list-disc list-inside space-y-1">
                    <li>{{ permissionUsage.roles }} role(s)</li>
                    <li>{{ permissionUsage.users }} user(s)</li>
                  </ul>
                  <p class="mt-2 text-xs text-blue-700">
                    Changes to this permission will affect all assigned users and roles.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Permission Preview -->
        <div v-if="permissionForm.name && permissionForm.module">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permission Preview</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-start space-x-4">
              <ShieldCheckIcon class="w-8 h-8 text-blue-600 mt-1" />
              <div class="flex-1">
                <h4 class="font-medium text-gray-900">{{ permissionForm.name }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ permissionForm.description || 'No description provided' }}</p>
                <div class="flex items-center mt-2 space-x-4">
                  <span class="text-xs text-gray-500">Module: <span class="font-medium">{{ formatModuleName(permissionForm.module) }}</span></span>
                  <span class="text-xs text-gray-500">ID: <span class="font-medium">{{ permissionForm.id }}</span></span>
                  <span :class="getStatusClass(permissionForm.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ getStatusText(permissionForm.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading || !permissionForm.name || !permissionForm.module"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Updating...' : 'Update Permission' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  ArrowLeftIcon,
  ShieldCheckIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline'
import { systemApi, type Permission } from '@/services/systemApi'
import { PERMISSION_MODULES } from '@/config/permissions'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const currentPermission = ref<Permission | null>(null)

// Permission form
const permissionForm = ref({
  id: 0,
  name: '',
  description: '',
  module: '',
  status: 1
})

// Permission usage data (mock for now)
const permissionUsage = ref<{ roles: number; users: number } | null>(null)

// Available modules
const modules = Object.values(PERMISSION_MODULES)

// Methods
const fetchPermission = async () => {
  const permissionId = route.params.id as string
  if (!permissionId) {
    router.push({ name: 'system-permissions' })
    return
  }

  try {
    // This would need a specific API endpoint to get a single permission
    // For now, we'll fetch all permissions and find the one we need
    const response = await systemApi.getPermissions({ limit: 1000 })
    if (response.status === 200) {
      const permissions = response.message?.data || []
      currentPermission.value = permissions.find(permission => permission.id === parseInt(permissionId)) || null
      
      if (currentPermission.value) {
        permissionForm.value = {
          id: currentPermission.value.id,
          name: currentPermission.value.name,
          description: currentPermission.value.description || '',
          module: currentPermission.value.module || '',
          status: currentPermission.value.status
        }
        
        // Mock usage data
        permissionUsage.value = {
          roles: Math.floor(Math.random() * 5) + 1,
          users: Math.floor(Math.random() * 20) + 1
        }
      } else {
        router.push({ name: 'system-permissions' })
      }
    }
  } catch (error) {
    console.error('Error fetching permission:', error)
    router.push({ name: 'system-permissions' })
  } finally {
    initialLoading.value = false
  }
}

const savePermission = async () => {
  if (!permissionForm.value.name.trim()) {
    alert('Permission name is required')
    return
  }

  if (!confirm('Are you sure you want to update this permission?')) {
    return
  }

  loading.value = true
  try {
    const response = await systemApi.updatePermission({
      id: permissionForm.value.id,
      name: permissionForm.value.name.trim(),
      description: permissionForm.value.description.trim(),
      module: permissionForm.value.module,
      status: permissionForm.value.status
    })

    if (response.status === 200) {
      alert('Permission updated successfully!')
      router.push({ name: 'system-permissions' })
    } else {
      alert('Error updating permission: ' + response.message)
    }
  } catch (error) {
    console.error('Error updating permission:', error)
    alert('Error updating permission')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-permissions' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Active',
    0: 'Inactive'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    0: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchPermission()
})
</script>
