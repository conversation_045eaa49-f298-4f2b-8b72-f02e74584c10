<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Partner Settings</h1>
        <p class="mt-1 text-sm text-gray-500">Manage partner API configurations and settings</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Settings
        </button>
      </div>
    </div>

    <!-- Partner Selection -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Select Partner</label>
          <select
            v-model="selectedPartnerId"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a partner...</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="loadPartnerSettings"
            :disabled="!selectedPartnerId"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Load Settings
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Display -->
    <div v-if="partnerSettings" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Settings for {{ getPartnerName(partnerSettings.partner_id) }}
        </h3>
      </div>
      
      <div class="p-6 space-y-6">
        <!-- API Configuration -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
            <div class="flex">
              <input
                :type="showApiKey ? 'text' : 'password'"
                :value="partnerSettings.api_key"
                readonly
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm"
              />
              <button
                @click="showApiKey = !showApiKey"
                class="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:text-gray-700"
              >
                <svg v-if="showApiKey" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
              </button>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">IP Address</label>
            <input
              :value="partnerSettings.ip_address || 'Not set'"
              readonly
              class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
            />
          </div>
        </div>

        <!-- URLs and Configuration -->
        <div class="grid grid-cols-1 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Callback URL</label>
            <input
              :value="partnerSettings.callback_url || 'Not set'"
              readonly
              class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
            />
          </div>
        </div>

        <!-- Financial Settings -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
            <input
              :value="partnerSettings.currency || 'KES'"
              readonly
              class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Billing Mode</label>
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                partnerSettings.billing_mode === 'prepay' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
              ]"
            >
              {{ partnerSettings.billing_mode === 'prepay' ? 'Prepaid' : 'Postpaid' }}
            </span>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Rate Limit</label>
            <div class="text-sm">
              <span class="font-medium">{{ partnerSettings.rate_limit || 60 }}</span>
              <span class="text-gray-500"> requests/minute</span>
            </div>
          </div>
        </div>

        <!-- Technical Settings -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
            <input
              :value="partnerSettings.timezone || 'Africa/Nairobi'"
              readonly
              class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Version</label>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              v{{ partnerSettings.version }}
            </span>
          </div>
        </div>

        <!-- Websites -->
        <div v-if="partnerSettings.websites && partnerSettings.websites.length > 0">
          <label class="block text-sm font-medium text-gray-700 mb-2">Authorized Websites</label>
          <div class="space-y-2">
            <div
              v-for="(website, index) in partnerSettings.websites"
              :key="index"
              class="flex items-center p-2 bg-gray-50 rounded-md"
            >
              <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
              </svg>
              <span class="text-sm text-gray-900">{{ website }}</span>
            </div>
          </div>
        </div>

        <!-- Metadata -->
        <div class="pt-4 border-t border-gray-200">
          <div class="text-sm text-gray-500">
            Created: {{ formatDate(partnerSettings.created_at, 'datetime') }}
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            @click="editSettings"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Edit Settings
          </button>
          <button
            @click="regenerateApiKey"
            class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            Regenerate API Key
          </button>
        </div>
      </div>
    </div>

    <!-- No Settings State -->
    <div v-else-if="selectedPartnerId && !loading" class="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No settings found</h3>
      <p class="mt-1 text-sm text-gray-500">This partner doesn't have settings configured yet.</p>
      <div class="mt-6">
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Create Settings
        </button>
      </div>
    </div>

    <!-- Create/Edit Settings Modal -->
    <PartnerSettingsModal
      :is-open="showCreateModal || showEditModal"
      :partner-settings="selectedPartnerSettings"
      :partner-id="selectedPartnerId"
      :mode="showEditModal ? 'edit' : 'create'"
      @close="closeModal"
      @saved="handleSettingsSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { partnerApi, type PartnerSettings, type Partner } from '@/services/partnerApi'
import { formatDate } from '@/utils/formatters'
import PartnerSettingsModal from '@/components/Modals/PartnerSettingsModal.vue'

// Reactive data
const loading = ref(false)
const partners = ref<Partner[]>([])
const partnerSettings = ref<PartnerSettings | null>(null)
const selectedPartnerId = ref('')
const showApiKey = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const selectedPartnerSettings = ref<PartnerSettings | null>(null)

// Methods
const loadPartners = async () => {
  try {
    const response = await partnerApi.getPartners({ limit: 100 })
    if (response.status === 200) {
      partners.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load partners:', error)
  }
}

const loadPartnerSettings = async () => {
  if (!selectedPartnerId.value) return
  
  loading.value = true
  try {
    const response = await partnerApi.getPartnerSettings(selectedPartnerId.value)
    if (response.status === 200) {
      partnerSettings.value = response.message
    } else {
      partnerSettings.value = null
    }
  } catch (error) {
    console.error('Failed to load partner settings:', error)
    partnerSettings.value = null
  } finally {
    loading.value = false
  }
}

const getPartnerName = (partnerId: number): string => {
  const partner = partners.value.find(p => p.id === partnerId)
  return partner?.name || `Partner ${partnerId}`
}

const editSettings = () => {
  selectedPartnerSettings.value = partnerSettings.value
  showEditModal.value = true
}

const regenerateApiKey = async () => {
  if (confirm('Are you sure you want to regenerate the API key? This will invalidate the current key.')) {
    try {
      // TODO: Implement API key regeneration
      console.log('Regenerate API key for partner:', selectedPartnerId.value)
    } catch (error) {
      console.error('Failed to regenerate API key:', error)
    }
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  selectedPartnerSettings.value = null
}

const handleSettingsSaved = () => {
  closeModal()
  loadPartnerSettings()
}

// Lifecycle
onMounted(() => {
  loadPartners()
})
</script>
