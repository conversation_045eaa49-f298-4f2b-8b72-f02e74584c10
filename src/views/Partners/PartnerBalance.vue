<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Partner Balance Management</h1>
        <p class="mt-1 text-sm text-gray-500">Monitor and manage partner account balances</p>
      </div>
    </div>

    <!-- Partner Selection -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Select Partner</label>
          <select
            v-model="selectedPartnerId"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a partner...</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="loadPartnerBalance"
            :disabled="!selectedPartnerId"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Load Balance
          </button>
        </div>
      </div>
    </div>

    <!-- Balance Overview -->
    <div v-if="partnerBalance" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Main Balance -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-blue-100">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Main Balance</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(partnerBalance.balance) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span
            :class="[
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              partnerBalance.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            ]"
          >
            {{ partnerBalance.status === 1 ? 'Active' : 'Inactive' }}
          </span>
        </div>
      </div>

      <!-- Bonus Balance -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-green-100">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Bonus Balance</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(partnerBalance.bonus) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 text-sm font-medium">Available for use</span>
        </div>
      </div>

      <!-- Total Balance -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-purple-100">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Balance</p>
            <p class="text-2xl font-semibold text-gray-900">
              {{ formatCurrency((partnerBalance.balance || 0) + (partnerBalance.bonus || 0)) }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-gray-600 text-sm">Last updated: {{ formatDate(partnerBalance.updated_at, 'datetime') }}</span>
        </div>
      </div>
    </div>

    <!-- Balance Summary -->
    <div v-if="balanceSummary" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Balance Summary</h3>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-2xl font-semibold text-blue-600">{{ formatCurrency(balanceSummary.total_deposits) }}</div>
            <div class="text-sm text-gray-500">Total Deposits</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-semibold text-red-600">{{ formatCurrency(balanceSummary.total_withdrawals) }}</div>
            <div class="text-sm text-gray-500">Total Withdrawals</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-semibold text-purple-600">{{ formatNumber(balanceSummary.total_bets) }}</div>
            <div class="text-sm text-gray-500">Total Bets</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-semibold text-green-600">{{ formatCurrency(balanceSummary.total_winnings) }}</div>
            <div class="text-sm text-gray-500">Total Winnings</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Transactions -->
    <div v-if="selectedPartnerId" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
        <router-link
          :to="`/partners/balance-transactions?partner_id=${selectedPartnerId}`"
          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View All
        </router-link>
      </div>
      
      <div class="p-6">
        <DataTable
          :data="recentTransactions"
          :headers="transactionHeaders"
          :loading="transactionsLoading"
          :pagination="false"
          :has-actions="false"
          empty-message="No recent transactions found"
        >
          <!-- Transaction Type -->
          <template #cell-transaction_type="{ item }">
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getTransactionTypeClass(item.transaction_type)
              ]"
            >
              {{ item.transaction_type }}
            </span>
          </template>

          <!-- Amount -->
          <template #cell-amount="{ item }">
            <span
              :class="[
                'font-medium',
                item.transaction_type === 'credit' ? 'text-green-600' : 'text-red-600'
              ]"
            >
              {{ item.transaction_type === 'credit' ? '+' : '-' }}{{ formatCurrency(Math.abs(item.amount)) }}
            </span>
          </template>

          <!-- Status -->
          <template #cell-status="{ item }">
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                item.status === 'completed' ? 'bg-green-100 text-green-800' : 
                item.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
              ]"
            >
              {{ item.status }}
            </span>
          </template>

          <!-- Created Date -->
          <template #cell-created_at="{ item }">
            <div class="text-sm text-gray-500">
              {{ formatDate(item.created_at, 'datetime') }}
            </div>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- No Balance State -->
    <div v-else-if="selectedPartnerId && !loading" class="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No balance information</h3>
      <p class="mt-1 text-sm text-gray-500">This partner doesn't have balance information available.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { partnerApi, type Partner, type PartnerBalance, type PartnerBalanceSummary, type PartnerTransaction } from '@/services/partnerApi'
import { formatCurrency, formatDate } from '@/utils/formatters'
import DataTable from '@/components/DataTable.vue'

// Reactive data
const loading = ref(false)
const transactionsLoading = ref(false)
const partners = ref<Partner[]>([])
const partnerBalance = ref<PartnerBalance | null>(null)
const balanceSummary = ref<PartnerBalanceSummary | null>(null)
const recentTransactions = ref<PartnerTransaction[]>([])
const selectedPartnerId = ref('')

// Table headers for transactions
const transactionHeaders = {
  transaction_type: 'Type',
  amount: 'Amount',
  status: 'Status',
  description: 'Description',
  created_at: 'Date'
}

// Methods
const loadPartners = async () => {
  try {
    const response = await partnerApi.getPartners({ limit: 100 })
    if (response.status === 200) {
      partners.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load partners:', error)
  }
}

const loadPartnerBalance = async () => {
  if (!selectedPartnerId.value) return
  
  loading.value = true
  try {
    // Load balance
    const balanceResponse = await partnerApi.getPartnerBalance(selectedPartnerId.value)
    if (balanceResponse.status === 200) {
      partnerBalance.value = balanceResponse.message
    }

    // Load balance summary
    const summaryResponse = await partnerApi.getPartnerBalanceSummary(selectedPartnerId.value)
    if (summaryResponse.status === 200) {
      balanceSummary.value = summaryResponse.message
    }

    // Load recent transactions
    await loadRecentTransactions()
  } catch (error) {
    console.error('Failed to load partner balance:', error)
  } finally {
    loading.value = false
  }
}

const loadRecentTransactions = async () => {
  if (!selectedPartnerId.value) return
  
  transactionsLoading.value = true
  try {
    const response = await partnerApi.getPartnerBalanceTransactions({
      partner_id: selectedPartnerId.value,
      limit: 5
    })
    
    if (response.status === 200) {
      recentTransactions.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load recent transactions:', error)
  } finally {
    transactionsLoading.value = false
  }
}

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num)
}

const getTransactionTypeClass = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'credit':
    case 'deposit':
      return 'bg-green-100 text-green-800'
    case 'debit':
    case 'withdrawal':
      return 'bg-red-100 text-red-800'
    case 'bet':
      return 'bg-blue-100 text-blue-800'
    case 'win':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Lifecycle
onMounted(() => {
  loadPartners()
})
</script>
