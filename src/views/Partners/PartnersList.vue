<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Partners Management</h1>
        <p class="mt-1 text-sm text-gray-500">Manage partner accounts and their services</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Partner
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search partners..."
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.status"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
          <input
            v-model="filters.start"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
          <input
            v-model="filters.end"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
    </div>

    <!-- Partners Table -->
    <DataTable
      :data="partners"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="pagination.current_page"
      :total-records="pagination.total"
      :page-size="pagination.limit"
      :has-actions="true"
      title="Partners"
      @page-change="handlePageChange"
      @limit-change="handleLimitChange"
    >
      <!-- Partner Info -->
      <template #cell-partner_info="{ item }">
        <div class="flex items-center">
          <div class="flex-shrink-0 h-10 w-10">
            <div class="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
              <span class="text-sm font-medium text-white">
                {{ getPartnerInitials(item.name) }}
              </span>
            </div>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
            <div class="text-sm text-gray-500">ID: {{ item.id }}</div>
          </div>
        </div>
      </template>

      <!-- Description -->
      <template #cell-description="{ item }">
        <div class="text-sm text-gray-900">
          {{ item.description || 'No description' }}
        </div>
      </template>

      <!-- Balance -->
      <template #cell-balance="{ item }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(item.balance || 0) }}
        </div>
      </template>

      <!-- Status -->
      <template #cell-status="{ item }">
        <span
          :class="[
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            item.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          ]"
        >
          {{ item.status === 1 ? 'Active' : 'Inactive' }}
        </span>
      </template>

      <!-- Created Date -->
      <template #cell-created_at="{ item }">
        <div class="text-sm text-gray-500">
          {{ formatDate(item.created_at, 'datetime') }}
        </div>
      </template>

      <!-- Actions -->
      <template #actions="{ item }">
        <button
          @click="editPartner(item)"
          class="text-blue-600 hover:text-blue-900 text-sm font-medium mr-3"
        >
          Edit
        </button>
        <button
          @click="viewPartnerDetails(item)"
          class="text-green-600 hover:text-green-900 text-sm font-medium mr-3"
        >
          View
        </button>
        <button
          @click="deletePartner(item)"
          class="text-red-600 hover:text-red-900 text-sm font-medium"
        >
          Delete
        </button>
      </template>
    </DataTable>

    <!-- Create/Edit Partner Modal -->
    <PartnerModal
      :is-open="showCreateModal || showEditModal"
      :partner="selectedPartner"
      :mode="showEditModal ? 'edit' : 'create'"
      @close="closeModal"
      @saved="handlePartnerSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { partnerApi, type Partner } from '@/services/partnerApi'
import { formatCurrency, formatDate } from '@/utils/formatters'
import DataTable from '@/components/DataTable.vue'
import PartnerModal from '@/components/Modals/PartnerModal.vue'

// Reactive data
const loading = ref(false)
const partners = ref<Partner[]>([])
const showCreateModal = ref(false)
const showEditModal = ref(false)
const selectedPartner = ref<Partner | null>(null)

const filters = reactive({
  search: '',
  status: '',
  start: '',
  end: ''
})

const pagination = reactive({
  current_page: 1,
  limit: 10,
  total: 0
})

// Table headers
const tableHeaders = {
  partner_info: 'Partner',
  description: 'Description',
  balance: 'Balance',
  status: 'Status',
  created_at: 'Created Date'
}

// Methods
const loadPartners = async () => {
  loading.value = true
  try {
    const response = await partnerApi.getPartners({
      page: pagination.current_page,
      limit: pagination.limit,
      search: filters.search,
      status: filters.status,
      start: filters.start,
      end: filters.end
    })
    
    if (response.status === 200) {
      partners.value = response.message.data || []
      pagination.total = response.message.total || 0
    }
  } catch (error) {
    console.error('Failed to load partners:', error)
  } finally {
    loading.value = false
  }
}

const getPartnerInitials = (name: string): string => {
  if (!name) return 'P'
  const parts = name.split(' ')
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase()
  }
  return name.substring(0, 2).toUpperCase()
}

const editPartner = (partner: Partner) => {
  selectedPartner.value = partner
  showEditModal.value = true
}

const viewPartnerDetails = (partner: Partner) => {
  // TODO: Navigate to partner details page
  console.log('View partner details:', partner)
}

const deletePartner = async (partner: Partner) => {
  if (confirm(`Are you sure you want to delete partner "${partner.name}"?`)) {
    try {
      const response = await partnerApi.deletePartner(partner.id)
      if (response.status === 200) {
        await loadPartners()
      }
    } catch (error) {
      console.error('Failed to delete partner:', error)
    }
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  selectedPartner.value = null
}

const handlePartnerSaved = () => {
  closeModal()
  loadPartners()
}

const handlePageChange = (page: number) => {
  pagination.current_page = page
  loadPartners()
}

const handleLimitChange = (limit: number) => {
  pagination.limit = limit
  pagination.current_page = 1
  loadPartners()
}

// Watch filters for auto-refresh
watch(filters, () => {
  pagination.current_page = 1
  loadPartners()
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadPartners()
})
</script>
