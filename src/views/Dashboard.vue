<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Welcome back{{ userName ? `, ${userName}` : '' }}!</h2>
          <p class="text-gray-600 mt-1">Here's what's happening with your financial platform today.</p>
        </div>
        <div class="hidden md:block">
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="text-blue-600 text-sm font-medium">Today's Date</div>
            <div class="text-blue-900 text-lg font-semibold">{{ currentDate }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="i in 4" :key="i" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-gray-200 w-12 h-12"></div>
          <div class="ml-4 flex-1">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-6 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Users -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-blue-100">
            <UsersIcon class="w-6 h-6 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Users</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(stats.total_users) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 text-sm font-medium">{{ stats.active_users }} active</span>
        </div>
      </div>

      <!-- Total Partners -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-green-100">
            <BuildingOfficeIcon class="w-6 h-6 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Partners</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(stats.total_partners) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 text-sm font-medium">{{ stats.active_partners }} active</span>
        </div>
      </div>

      <!-- Total Revenue -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-purple-100">
            <CurrencyDollarIcon class="w-6 h-6 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(stats.total_revenue) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 text-sm font-medium">{{ formatNumber(stats.total_bets) }}</span>
          <span class="text-gray-600 text-sm ml-1">total bets</span>
        </div>
      </div>

      <!-- System Users -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-orange-100">
            <UsersIcon class="w-6 h-6 text-orange-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">System Users</p>
            <p class="text-2xl font-semibold text-gray-900">156</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-green-600 text-sm font-medium">+3%</span>
          <span class="text-gray-600 text-sm ml-1">from last month</span>
        </div>
      </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Transactions -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Recent Transactions</h3>
          <router-link :to="{ name: 'transactions' }" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View all
          </router-link>
        </div>
        <div class="space-y-4">
          <div v-for="transaction in recentTransactions" :key="transaction.id" class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <CreditCardIcon class="w-5 h-5 text-gray-600" />
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">{{ transaction.description }}</p>
                <p class="text-xs text-gray-500">{{ transaction.time }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium" :class="transaction.amount > 0 ? 'text-green-600' : 'text-red-600'">
                {{ transaction.amount > 0 ? '+' : '' }}${{ Math.abs(transaction.amount).toLocaleString() }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
          <!-- <router-link :to="{ name: 'organisations' }" class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
            <BuildingOfficeIcon class="w-8 h-8 text-blue-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Manage Organizations</p>
          </router-link>
          <router-link :to="{ name: 'requests' }" class="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200">
            <CurrencyDollarIcon class="w-8 h-8 text-green-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Loan Requests</p>
          </router-link>
          <router-link :to="{ name: 'customers' }" class="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors duration-200">
            <MagnifyingGlassIcon class="w-8 h-8 text-purple-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Search Customers</p>
          </router-link> -->
          <router-link :to="{ name: 'system-roles' }" class="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors duration-200">
            <UsersIcon class="w-8 h-8 text-orange-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">System Roles</p>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Layout Test Section -->
    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border-2 border-green-200 p-6">
      <h3 class="text-lg font-semibold text-green-800 mb-2">🎉 New Layout Working!</h3>
      <p class="text-green-700 mb-4">
        This is the new Vue 3 + Vite + Pinia dashboard with modern layout.
        The sidebar is on the left, this content is on the right - perfectly positioned side by side!
      </p>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div class="bg-white rounded-lg p-3">
          <strong class="text-gray-900">Framework:</strong>
          <p class="text-gray-600">Vue 3 + TypeScript</p>
        </div>
        <div class="bg-white rounded-lg p-3">
          <strong class="text-gray-900">Build Tool:</strong>
          <p class="text-gray-600">Vite (Fast & Modern)</p>
        </div>
        <div class="bg-white rounded-lg p-3">
          <strong class="text-gray-900">State Management:</strong>
          <p class="text-gray-600">Pinia (Vue 3 Official)</p>
        </div>
      </div>
    </div>

    <!-- CSS Debug Test -->
    <div class="bg-red-500 text-white p-4 rounded-lg">
      <h3 class="text-xl font-bold">🔴 CSS DEBUG TEST</h3>
      <p class="mt-2">If this box is RED with WHITE text, Tailwind CSS is working!</p>
      <div class="mt-4 flex space-x-4">
        <div class="bg-blue-600 px-4 py-2 rounded">Blue Button</div>
        <div class="bg-green-600 px-4 py-2 rounded">Green Button</div>
        <div class="bg-purple-600 px-4 py-2 rounded">Purple Button</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { dashboardApi, type DashboardStats } from '@/services/dashboardApi'
import { formatCurrency } from '@/utils/formatters'
import {
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  UsersIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

// Auth store
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const stats = ref<DashboardStats>({
  total_users: 0,
  active_users: 0,
  total_partners: 0,
  active_partners: 0,
  total_bets: 0,
  total_revenue: 0,
  pending_transactions: 0,
  failed_transactions: 0
})

// User name from auth store
const userName = computed(() => {
  return authStore.user?.display_name || authStore.user?.username || null
})

// Current date
const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Methods
const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num)
}

const loadDashboardStats = async () => {
  loading.value = true
  try {
    const response = await dashboardApi.getStats({
      partner_id: authStore.selectedPartnerId || ''
    })

    if (response.status === 200) {
      stats.value = response.message
    }
  } catch (error) {
    console.error('Failed to load dashboard stats:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadDashboardStats()
})

// Sample recent transactions
const recentTransactions = ref([
  { id: 1, description: 'Loan Payment Received', amount: 2500, time: '2 minutes ago' },
  { id: 2, description: 'Withdrawal Request', amount: -1200, time: '15 minutes ago' },
  { id: 3, description: 'Bill Payment', amount: -350, time: '1 hour ago' },
  { id: 4, description: 'Deposit', amount: 5000, time: '2 hours ago' },
  { id: 5, description: 'Transfer', amount: -800, time: '3 hours ago' }
])
</script>
