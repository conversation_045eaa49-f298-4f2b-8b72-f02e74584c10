<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Services</h1>
          <p class="mt-1 text-sm text-gray-600">
            Manage and monitor all platform services and integrations
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="refreshData"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button
            @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Service Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Healthy Services</p>
            <p class="text-2xl font-semibold text-gray-900">{{ healthyServices }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Warning Services</p>
            <p class="text-2xl font-semibold text-gray-900">{{ warningServices }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Critical Services</p>
            <p class="text-2xl font-semibold text-gray-900">{{ criticalServices }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Services</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalServices }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Service Status</label>
          <select
            id="status-filter"
            v-model="filters.service_status"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Statuses</option>
            <option value="healthy">Healthy</option>
            <option value="warning">Warning</option>
            <option value="critical">Critical</option>
            <option value="maintenance">Maintenance</option>
          </select>
        </div>
        <div>
          <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
          <select
            id="category-filter"
            v-model="filters.service_category"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Categories</option>
            <option value="core">Core</option>
            <option value="integration">Integration</option>
            <option value="analytics">Analytics</option>
            <option value="monitoring">Monitoring</option>
          </select>
        </div>
        <div>
          <label for="environment-filter" class="block text-sm font-medium text-gray-700 mb-2">Environment</label>
          <select
            id="environment-filter"
            v-model="filters.environment"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Environments</option>
            <option value="production">Production</option>
            <option value="staging">Staging</option>
            <option value="development">Development</option>
          </select>
        </div>
        <div>
          <label for="version-filter" class="block text-sm font-medium text-gray-700 mb-2">Version</label>
          <select
            id="version-filter"
            v-model="filters.service_version"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Versions</option>
            <option value="v1.0">v1.0</option>
            <option value="v1.1">v1.1</option>
            <option value="v2.0">v2.0</option>
            <option value="v2.1">v2.1</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Services Table -->
    <DataTable
      :data="services"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Platform Services"
      row-key="service_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="showAddModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add Service
          </button>
        </div>
      </template>

      <!-- Custom Service Status Cell -->
      <template #cell-service_status="{ value }">
        <span
          :class="{
            'bg-green-100 text-green-800': value === 'healthy',
            'bg-yellow-100 text-yellow-800': value === 'warning',
            'bg-red-100 text-red-800': value === 'critical',
            'bg-gray-100 text-gray-800': value === 'maintenance'
          }"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ value ? value.charAt(0).toUpperCase() + value.slice(1) : '-' }}
        </span>
      </template>

      <!-- Custom Service Category Cell -->
      <template #cell-service_category="{ value }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {{ value ? value.charAt(0).toUpperCase() + value.slice(1) : '-' }}
        </span>
      </template>

      <!-- Custom Uptime Cell -->
      <template #cell-uptime_percentage="{ value }">
        <div class="flex items-center">
          <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
            <div 
              class="h-2 rounded-full"
              :class="{
                'bg-green-500': value >= 99,
                'bg-yellow-500': value >= 95 && value < 99,
                'bg-red-500': value < 95
              }"
              :style="{ width: `${value}%` }"
            ></div>
          </div>
          <span class="text-sm font-medium text-gray-900">{{ value }}%</span>
        </div>
      </template>

      <!-- Custom Response Time Cell -->
      <template #cell-avg_response_time="{ value }">
        <span class="text-gray-900 font-medium">
          {{ value }}ms
        </span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <div class="flex items-center space-x-2">
          <button
            @click="viewService(item)"
            class="text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"
          >
            View
          </button>
          <button
            @click="editService(item)"
            class="text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"
          >
            Edit
          </button>
          <button
            @click="viewLogs(item)"
            class="text-green-600 hover:text-green-900 text-sm font-medium transition-colors duration-200"
          >
            Logs
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { servicesApi } from '@/services/servicesApi'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const services = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)

// Filters
const filters = reactive({
  service_status: '',
  service_category: '',
  environment: '',
  service_version: ''
})

// Remove sample data - using real API calls

// Computed properties for summary cards
const totalServices = computed(() => services.value.length)
const healthyServices = computed(() => 
  services.value.filter(service => service.service_status === 'healthy').length
)
const warningServices = computed(() => 
  services.value.filter(service => service.service_status === 'warning').length
)
const criticalServices = computed(() => 
  services.value.filter(service => service.service_status === 'critical').length
)

// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await servicesApi.getServices({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: filters.service_status,
      start: '',
      end: ''
    })

    if (response.status === 200) {
      services.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load services:', response)
      services.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error loading services:', error)
    services.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewService(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewService = (service: any) => {
  console.log('View service:', service)
  // Implement view logic
}

const editService = (service: any) => {
  console.log('Edit service:', service)
  // Implement edit logic
}

const viewLogs = (service: any) => {
  console.log('View logs for service:', service)
  // Implement logs view logic
}

const exportData = () => {
  console.log('Export services data')
  // Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
