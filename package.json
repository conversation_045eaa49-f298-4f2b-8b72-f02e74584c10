{"name": "mossbets-b2b-dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 1414", "build": "run-p type-check \"build-only {@}\" --", "build-staging": "vite build && cp -R dist/* ../mossbets_b2b_bo_ui_pro", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@tailwindcss/postcss": "^4.1.8", "@vueuse/core": "^13.6.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tsconfig/node22": "^22.0.1", "@types/axios": "^0.9.36", "@types/crypto-js": "^4.2.2", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "npm-run-all2": "^7.0.2", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}