# Deployment Guide - Fixing SPA Routing Issues

## Problem
When deploying a Vue.js Single Page Application (SPA), users encounter "404 Not Found" errors when:
- Refreshing the page on any route other than the root (`/`)
- Directly accessing URLs like `/partners`, `/dashboard`, etc.
- Navigating back/forward using browser buttons

## Root Cause
SPAs use client-side routing, meaning all routes are handled by JavaScript in the browser. When a user visits `/partners` directly or refreshes the page, the web server looks for a physical file at that path, which doesn't exist.

## Solutions

### 1. Nginx Configuration (Recommended)

Use the provided `nginx.conf` file in this repository. Key configuration:

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

This tells nginx to:
1. Try to serve the requested file (`$uri`)
2. Try to serve it as a directory (`$uri/`)
3. Fall back to serving `index.html` (which loads the Vue app)

#### Steps to implement:
1. Copy the `nginx.conf` file to your server
2. Update the `server_name` and `root` paths for your environment
3. Update the API proxy configuration if needed
4. Reload nginx: `sudo nginx -s reload`

### 2. Apache Configuration

If using Apache, create/update `.htaccess` in your build directory:

```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

### 3. Other Web Servers

#### Caddy
```
try_files {path} /index.html
```

#### Express.js (Node.js)
```javascript
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/index.html'));
});
```

## Build Configuration

Ensure your build is configured correctly for production:

### 1. Update `vite.config.ts`
```typescript
export default defineConfig({
  base: '/', // Use '/' for root deployment, or '/subdirectory/' if deploying to a subdirectory
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Set to true for debugging
  }
})
```

### 2. Build the application
```bash
npm run build
```

### 3. Deploy the `dist` folder contents to your web server

## Testing the Fix

After implementing the nginx configuration:

1. Build and deploy your application
2. Visit your domain directly
3. Navigate to different routes (e.g., `/partners`, `/dashboard`)
4. Refresh the page - it should load correctly
5. Use browser back/forward buttons - should work without errors

## Additional Considerations

### 1. API Proxy Configuration
If your API is on a different domain, update the nginx proxy configuration:

```nginx
location /api/ {
    proxy_pass https://your-api-domain.com/;
    # ... other proxy settings
}
```

### 2. HTTPS Configuration
For production, always use HTTPS. Uncomment and configure the HTTPS block in `nginx.conf`.

### 3. Caching Strategy
The provided configuration includes:
- Long-term caching for static assets (JS, CSS, images)
- No caching for HTML files (to ensure updates are loaded)

### 4. Security Headers
The configuration includes basic security headers. Adjust based on your security requirements.

## Troubleshooting

### Still getting 404 errors?
1. Check nginx error logs: `sudo tail -f /var/log/nginx/error.log`
2. Verify the `root` path in nginx configuration points to your build directory
3. Ensure the build directory contains `index.html`
4. Check file permissions

### API calls failing?
1. Verify the API proxy configuration
2. Check CORS settings on your API server
3. Ensure the API endpoints are accessible from the server

### Performance issues?
1. Enable gzip compression (included in the config)
2. Optimize your build with code splitting
3. Use a CDN for static assets

## Environment-Specific Notes

### Development
The Vite dev server already handles SPA routing correctly.

### Staging/Production
Always test the routing fix in a staging environment before deploying to production.

## Support
If you continue to experience issues, check:
1. Web server error logs
2. Browser developer console for errors
3. Network tab for failed requests
4. Ensure all environment variables are set correctly
